// 全新的Supabase初始化脚本 - 避免重复声明问题
console.log('🔧 [SUPABASE-INIT] 开始初始化Supabase');

// 检查是否已经初始化过
if (typeof window.supabaseInitialized === 'undefined') {
    console.log('🔧 [SUPABASE-INIT] 首次初始化');
    
    // 标记为已初始化
    window.supabaseInitialized = true;
    
    // Supabase配置
    const SUPABASE_URL = 'https://snckktsqwrbfwtjlvcfr.supabase.co';
    const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644';
    
    // 检查Supabase库是否加载
    if (typeof window.supabase !== 'undefined' && typeof window.supabase.createClient === 'function') {
        console.log('🔧 [SUPABASE-INIT] Supabase库已加载，创建客户端');
        
        // 创建Supabase客户端
        window.supabaseClient = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        // 设置全局引用（兼容性）
        window.supabase = window.supabaseClient;
        
        console.log('🔧 [SUPABASE-INIT] Supabase客户端创建成功');
        
        // 用户权限类型
        window.USER_TYPES = {
            GUEST: 'guest',
            PREMIUM: 'premium',
            PRIVILEGED: 'privileged',
            ADMIN: 'admin'
        };
        
        // 全局用户状态
        window.currentUser = null;
        window.currentUserType = 'guest';
        
        console.log('🔧 [SUPABASE-INIT] 初始化完成');
        
        // 触发初始化完成事件
        window.dispatchEvent(new CustomEvent('supabaseReady'));
        
    } else {
        console.error('🔧 [SUPABASE-INIT] Supabase库未正确加载');
    }
} else {
    console.log('🔧 [SUPABASE-INIT] 已经初始化过，跳过');
}
