<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重置tex用户密码</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .user-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin: 10px 0;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 重置tex用户密码</h1>
        
        <div class="user-info">
            <h3>用户信息：</h3>
            <p><strong>用户名:</strong> tex</p>
            <p><strong>邮箱:</strong> <EMAIL></p>
            <p><strong>权限:</strong> privileged (特许用户)</p>
            <p><strong>目标密码:</strong> 123456</p>
        </div>
        
        <button id="sendResetBtn" onclick="sendPasswordReset()">
            发送密码重置邮件到 <EMAIL>
        </button>
        
        <button id="testLoginBtn" onclick="testLogin()" style="background-color: #28a745;">
            测试登录 (tex / 123456)
        </button>
        
        <div id="message" class="message"></div>
        
        <div style="margin-top: 30px; padding: 15px; background-color: #fff3cd; border-radius: 5px;">
            <h4>操作步骤：</h4>
            <ol>
                <li>点击"发送密码重置邮件"按钮</li>
                <li>检查 <EMAIL> 邮箱</li>
                <li>点击邮件中的重置链接</li>
                <li>在重置页面输入新密码：<strong>123456</strong></li>
                <li>完成后点击"测试登录"验证</li>
            </ol>
        </div>
    </div>

    <!-- 引入 Supabase -->
    <script src="supabase-js.min.js"></script>
    <script src="js/supabase-config.js"></script>
    
    <script>
        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = text;
            messageDiv.style.display = 'block';
        }

        async function sendPasswordReset() {
            const sendBtn = document.getElementById('sendResetBtn');
            
            try {
                sendBtn.disabled = true;
                sendBtn.textContent = '发送中...';
                showMessage('正在发送密码重置邮件到 <EMAIL>...', 'info');

                const { error } = await supabase.auth.resetPasswordForEmail('<EMAIL>', {
                    redirectTo: `${window.location.origin}/reset-password-complete.html`
                });

                if (error) {
                    throw error;
                }

                showMessage(`✅ 密码重置邮件已发送到 <strong><EMAIL></strong><br><br>
                    请按照以下步骤操作：<br>
                    1. 检查邮箱（包括垃圾邮件文件夹）<br>
                    2. 点击邮件中的重置链接<br>
                    3. 在重置页面输入新密码：<strong>123456</strong><br>
                    4. 完成后返回此页面测试登录`, 'success');

            } catch (error) {
                console.error('发送重置邮件失败:', error);
                showMessage('❌ 发送失败: ' + error.message, 'error');
            } finally {
                sendBtn.disabled = false;
                sendBtn.textContent = '发送密码重置邮件到 <EMAIL>';
            }
        }

        async function testLogin() {
            const testBtn = document.getElementById('testLoginBtn');
            
            try {
                testBtn.disabled = true;
                testBtn.textContent = '测试中...';
                showMessage('正在测试登录 tex / 123456...', 'info');

                // 先登出当前用户
                await supabase.auth.signOut();

                // 尝试登录
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: '<EMAIL>',
                    password: '123456'
                });

                if (error) {
                    throw error;
                }

                showMessage(`✅ 登录成功！<br>
                    用户: ${data.user.email}<br>
                    登录时间: ${new Date().toLocaleString()}<br><br>
                    tex用户密码已成功重置为 123456`, 'success');

                // 登出测试用户
                setTimeout(async () => {
                    await supabase.auth.signOut();
                    showMessage('✅ 测试完成，已自动登出', 'info');
                }, 3000);

            } catch (error) {
                console.error('登录测试失败:', error);
                if (error.message.includes('Invalid login credentials')) {
                    showMessage('❌ 登录失败：密码尚未重置成功<br>请确保已通过邮件链接完成密码重置', 'error');
                } else {
                    showMessage('❌ 登录测试失败: ' + error.message, 'error');
                }
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '测试登录 (tex / 123456)';
            }
        }

        // 页面加载时检查系统状态
        window.addEventListener('load', function() {
            setTimeout(() => {
                if (typeof supabase === 'undefined') {
                    showMessage('❌ Supabase 未正确加载，请刷新页面重试', 'error');
                } else {
                    showMessage('✅ 系统已就绪，可以开始重置密码', 'info');
                }
            }, 1000);
        });
    </script>
</body>
</html>
