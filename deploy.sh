#!/bin/bash

# 春盛机械网站自动部署脚本
# 适用于CentOS 8云主机

set -e  # 遇到错误立即退出

echo "=========================================="
echo "     春盛机械网站自动部署脚本"
echo "=========================================="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 获取用户输入
get_user_input() {
    echo ""
    log_info "请提供以下信息："
    
    read -p "域名 (例如: example.com): " DOMAIN
    read -p "是否配置SSL证书? (y/n): " SETUP_SSL
    read -p "网站目录 (默认: /var/www/chunsheng): " WEB_DIR
    
    # 设置默认值
    WEB_DIR=${WEB_DIR:-/var/www/chunsheng}
    
    echo ""
    log_info "配置信息："
    echo "域名: $DOMAIN"
    echo "SSL证书: $SETUP_SSL"
    echo "网站目录: $WEB_DIR"
    echo ""
    
    read -p "确认以上配置? (y/n): " CONFIRM
    if [[ $CONFIRM != "y" ]]; then
        log_error "部署已取消"
        exit 1
    fi
}

# 更新系统
update_system() {
    log_info "更新系统包..."
    dnf update -y
    dnf install -y wget curl git unzip vim
    log_success "系统更新完成"
}

# 安装Nginx
install_nginx() {
    log_info "安装Nginx..."
    dnf install -y nginx
    systemctl start nginx
    systemctl enable nginx
    log_success "Nginx安装完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    firewall-cmd --permanent --add-service=http
    firewall-cmd --permanent --add-service=https
    firewall-cmd --permanent --add-port=80/tcp
    firewall-cmd --permanent --add-port=443/tcp
    firewall-cmd --reload
    log_success "防火墙配置完成"
}

# 创建网站目录
create_web_directory() {
    log_info "创建网站目录..."
    mkdir -p $WEB_DIR
    
    # 创建示例index.html（如果不存在）
    if [[ ! -f "$WEB_DIR/index.html" ]]; then
        cat > $WEB_DIR/index.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>春盛机械 - 网站部署成功</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 100px; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .success { color: #28a745; font-size: 24px; margin-bottom: 20px; }
        .info { color: #666; line-height: 1.6; }
    </style>
</head>
<body>
    <div class="container">
        <div class="success">🎉 网站部署成功！</div>
        <div class="info">
            <p>春盛机械网站已成功部署到云主机</p>
            <p>请上传您的网站文件到服务器</p>
            <p>部署时间: <script>document.write(new Date().toLocaleString());</script></p>
        </div>
    </div>
</body>
</html>
EOF
    fi
    
    chown -R nginx:nginx $WEB_DIR
    chmod -R 755 $WEB_DIR
    log_success "网站目录创建完成"
}

# 配置Nginx
configure_nginx() {
    log_info "配置Nginx..."
    
    # 备份原配置
    cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup
    
    # 创建优化的主配置
    cat > /etc/nginx/nginx.conf << 'EOF'
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 20M;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    include /etc/nginx/conf.d/*.conf;
}
EOF

    # 创建站点配置
    cat > /etc/nginx/conf.d/chunsheng.conf << EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    root $WEB_DIR;
    index index.html index.htm;

    access_log /var/log/nginx/chunsheng_access.log;
    error_log /var/log/nginx/chunsheng_error.log;

    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location / {
        try_files \$uri \$uri/ =404;
    }

    # 管理后台
    location /admin/ {
        try_files \$uri \$uri/ =404;
    }

    # 安全头部
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    server_tokens off;
}
EOF

    # 测试配置
    nginx -t
    systemctl restart nginx
    log_success "Nginx配置完成"
}

# 安装SSL证书
install_ssl() {
    if [[ $SETUP_SSL == "y" ]]; then
        log_info "安装SSL证书..."
        dnf install -y python3-certbot-nginx
        
        log_warning "请确保域名已正确解析到此服务器IP"
        read -p "域名解析完成后按回车继续..."
        
        certbot --nginx -d $DOMAIN -d www.$DOMAIN --non-interactive --agree-tos --email admin@$DOMAIN
        
        # 设置自动续期
        echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
        log_success "SSL证书安装完成"
    fi
}

# 设置SELinux
configure_selinux() {
    if command -v getenforce &> /dev/null; then
        if [[ $(getenforce) != "Disabled" ]]; then
            log_info "配置SELinux..."
            setsebool -P httpd_can_network_connect 1
            restorecon -R $WEB_DIR
            log_success "SELinux配置完成"
        fi
    fi
}

# 创建上传脚本
create_upload_script() {
    log_info "创建文件上传脚本..."
    
    cat > /root/upload_website.sh << EOF
#!/bin/bash
# 网站文件上传脚本

echo "上传网站文件到 $WEB_DIR"
echo "使用方法："
echo "1. scp -r local_files/* root@server_ip:$WEB_DIR/"
echo "2. rsync -avz local_files/ root@server_ip:$WEB_DIR/"
echo ""
echo "上传完成后运行："
echo "chown -R nginx:nginx $WEB_DIR"
echo "chmod -R 755 $WEB_DIR"
echo "systemctl restart nginx"
EOF

    chmod +x /root/upload_website.sh
    log_success "上传脚本创建完成: /root/upload_website.sh"
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "=========================================="
    log_success "部署完成！"
    echo "=========================================="
    echo ""
    echo "网站信息："
    echo "  域名: http://$DOMAIN"
    if [[ $SETUP_SSL == "y" ]]; then
        echo "  HTTPS: https://$DOMAIN"
    fi
    echo "  网站目录: $WEB_DIR"
    echo "  Nginx配置: /etc/nginx/conf.d/chunsheng.conf"
    echo ""
    echo "日志文件："
    echo "  访问日志: /var/log/nginx/chunsheng_access.log"
    echo "  错误日志: /var/log/nginx/chunsheng_error.log"
    echo ""
    echo "下一步："
    echo "1. 上传网站文件到 $WEB_DIR"
    echo "2. 运行: chown -R nginx:nginx $WEB_DIR"
    echo "3. 运行: systemctl restart nginx"
    echo ""
    echo "常用命令："
    echo "  查看Nginx状态: systemctl status nginx"
    echo "  重启Nginx: systemctl restart nginx"
    echo "  查看访问日志: tail -f /var/log/nginx/chunsheng_access.log"
    echo ""
}

# 主函数
main() {
    check_root
    get_user_input
    
    log_info "开始部署..."
    
    update_system
    install_nginx
    configure_firewall
    create_web_directory
    configure_nginx
    install_ssl
    configure_selinux
    create_upload_script
    
    show_deployment_info
}

# 运行主函数
main "$@"
