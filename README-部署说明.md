# 春晟机械网站部署说明

## 🎯 快速部署（推荐）

### 步骤1：连接到您的Linux服务器
通过VNC连接到您的服务器：
```
http://203.104.45.250:60001/vnc_auto.html?token=9556352f-c69d-4c78-bf55-998c8477498a&instance_name=test01
```

### 步骤2：上传部署文件
将以下文件上传到服务器：
- `一键部署.sh` - 自动部署脚本
- `上传网站文件.sh` - 文件上传脚本
- 整个 `chunsheng-website` 文件夹

### 步骤3：运行一键部署
```bash
# 进入上传的目录
cd /path/to/chunsheng-website

# 给脚本执行权限
chmod +x 一键部署.sh

# 运行部署脚本（需要root权限）
sudo ./一键部署.sh
```

### 步骤4：上传网站文件
```bash
# 在网站文件目录中运行
chmod +x 上传网站文件.sh
sudo ./上传网站文件.sh
```

## 📋 部署后检查

1. **访问网站**
   - 打开浏览器访问：`http://您的服务器IP`
   - 应该能看到网站首页

2. **测试功能**
   - 产品页面：`http://您的服务器IP/products.html`
   - 管理后台：`http://您的服务器IP/admin/dashboard.html`
   - 用户注册：`http://您的服务器IP/register.html`

3. **检查服务状态**
   ```bash
   sudo systemctl status nginx
   ```

## 🔧 手动部署（备选方案）

如果自动部署遇到问题，可以按照 `快速部署指南.md` 进行手动部署。

## 📁 文件结构

```
chunsheng-website/
├── index.html              # 网站首页
├── products.html           # 产品页面
├── admin/                  # 管理后台
│   ├── dashboard.html
│   ├── products.html
│   └── users.html
├── js/                     # JavaScript文件
│   ├── supabase-config.js  # 数据库配置
│   └── main.js
├── css/                    # 样式文件
├── 一键部署.sh             # 自动部署脚本
├── 上传网站文件.sh         # 文件上传脚本
└── 快速部署指南.md         # 详细部署指南
```

## 🌐 访问地址

部署完成后，您可以通过以下地址访问：

- **网站首页**: http://您的服务器IP
- **产品中心**: http://您的服务器IP/products.html
- **关于我们**: http://您的服务器IP/about.html
- **联系我们**: http://您的服务器IP/contact.html
- **用户登录**: http://您的服务器IP/login.html
- **用户注册**: http://您的服务器IP/register.html
- **管理后台**: http://您的服务器IP/admin/dashboard.html

## 🔐 管理后台功能

- **用户管理**: 查看和管理注册用户
- **产品管理**: 添加、编辑、删除产品
- **客服管理**: 处理客户咨询
- **权限控制**: 管理用户下载权限

## 🛠️ 常用维护命令

```bash
# 重启Nginx
sudo systemctl restart nginx

# 查看Nginx状态
sudo systemctl status nginx

# 查看访问日志
sudo tail -f /var/log/nginx/chunsheng_access.log

# 查看错误日志
sudo tail -f /var/log/nginx/chunsheng_error.log

# 更新网站文件后重新设置权限
sudo chown -R nginx:nginx /var/www/chunsheng
sudo chmod -R 755 /var/www/chunsheng
```

## 🔍 故障排除

### 1. 无法访问网站
```bash
# 检查防火墙
sudo firewall-cmd --list-all

# 检查Nginx状态
sudo systemctl status nginx

# 检查端口监听
sudo ss -tlnp | grep :80
```

### 2. 403 Forbidden错误
```bash
# 检查文件权限
ls -la /var/www/chunsheng/

# 重新设置权限
sudo chown -R nginx:nginx /var/www/chunsheng
sudo chmod -R 755 /var/www/chunsheng
```

### 3. 502 Bad Gateway错误
```bash
# 检查Nginx配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

## 📞 技术支持

如果遇到问题，请提供：
1. 错误信息截图
2. 服务器日志：`sudo tail -20 /var/log/nginx/error.log`
3. 系统信息：`cat /etc/os-release`

## 🎉 部署成功标志

当您看到以下内容时，说明部署成功：
- ✅ 能够访问网站首页
- ✅ 产品页面正常显示
- ✅ 管理后台可以访问
- ✅ Nginx服务正常运行
- ✅ 防火墙端口已开放
