/**
 * 本地验证码组件
 * 支持数字、字母混合验证码，带干扰线和噪点
 */
class LocalCaptcha {
    constructor(canvasId, options = {}) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.options = {
            width: options.width || 120,
            height: options.height || 40,
            length: options.length || 4,
            fontSize: options.fontSize || 20,
            chars: options.chars || 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789',
            backgroundColor: options.backgroundColor || '#f8f9fa',
            fontColor: options.fontColor || '#333',
            lineColor: options.lineColor || '#666',
            dotColor: options.dotColor || '#999'
        };
        
        this.code = '';
        this.init();
    }
    
    init() {
        this.canvas.width = this.options.width;
        this.canvas.height = this.options.height;
        this.canvas.style.cursor = 'pointer';
        this.canvas.style.border = '1px solid #ddd';
        this.canvas.style.borderRadius = '4px';
        
        // 点击刷新验证码
        this.canvas.addEventListener('click', () => {
            this.refresh();
        });
        
        this.refresh();
    }
    
    // 生成随机验证码
    generateCode() {
        let code = '';
        for (let i = 0; i < this.options.length; i++) {
            const randomIndex = Math.floor(Math.random() * this.options.chars.length);
            code += this.options.chars[randomIndex];
        }
        return code;
    }
    
    // 绘制背景
    drawBackground() {
        this.ctx.fillStyle = this.options.backgroundColor;
        this.ctx.fillRect(0, 0, this.options.width, this.options.height);
    }
    
    // 绘制干扰线
    drawLines() {
        const lineCount = 3 + Math.floor(Math.random() * 3); // 3-5条线
        
        for (let i = 0; i < lineCount; i++) {
            this.ctx.strokeStyle = this.getRandomColor(this.options.lineColor);
            this.ctx.lineWidth = 1 + Math.random();
            this.ctx.beginPath();
            
            const x1 = Math.random() * this.options.width;
            const y1 = Math.random() * this.options.height;
            const x2 = Math.random() * this.options.width;
            const y2 = Math.random() * this.options.height;
            
            this.ctx.moveTo(x1, y1);
            this.ctx.lineTo(x2, y2);
            this.ctx.stroke();
        }
    }
    
    // 绘制噪点
    drawDots() {
        const dotCount = 20 + Math.floor(Math.random() * 20); // 20-40个点
        
        for (let i = 0; i < dotCount; i++) {
            this.ctx.fillStyle = this.getRandomColor(this.options.dotColor);
            this.ctx.beginPath();
            
            const x = Math.random() * this.options.width;
            const y = Math.random() * this.options.height;
            const radius = 1 + Math.random() * 2;
            
            this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
            this.ctx.fill();
        }
    }
    
    // 绘制验证码文字
    drawText() {
        const charWidth = this.options.width / this.options.length;
        
        for (let i = 0; i < this.code.length; i++) {
            const char = this.code[i];
            
            // 随机字体大小
            const fontSize = this.options.fontSize + Math.random() * 6 - 3;
            this.ctx.font = `${fontSize}px Arial, sans-serif`;
            
            // 随机颜色
            this.ctx.fillStyle = this.getRandomColor(this.options.fontColor);
            
            // 随机位置和角度
            const x = charWidth * i + charWidth / 2 + (Math.random() * 10 - 5);
            const y = this.options.height / 2 + fontSize / 3 + (Math.random() * 6 - 3);
            const angle = (Math.random() * 30 - 15) * Math.PI / 180;
            
            this.ctx.save();
            this.ctx.translate(x, y);
            this.ctx.rotate(angle);
            this.ctx.textAlign = 'center';
            this.ctx.fillText(char, 0, 0);
            this.ctx.restore();
        }
    }
    
    // 获取随机颜色变化
    getRandomColor(baseColor) {
        const colors = [
            '#333', '#666', '#999', '#555', '#777',
            '#2c3e50', '#34495e', '#7f8c8d', '#95a5a6'
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    }
    
    // 刷新验证码
    refresh() {
        this.code = this.generateCode();
        this.draw();
    }
    
    // 绘制验证码
    draw() {
        // 清空画布
        this.ctx.clearRect(0, 0, this.options.width, this.options.height);
        
        // 绘制各个元素
        this.drawBackground();
        this.drawLines();
        this.drawDots();
        this.drawText();
    }
    
    // 验证输入的验证码
    validate(inputCode) {
        if (!inputCode || !this.code) {
            return false;
        }
        return inputCode.toLowerCase() === this.code.toLowerCase();
    }
    
    // 获取当前验证码（用于调试，生产环境不应暴露）
    getCode() {
        return this.code;
    }
}

// 验证码管理器
class CaptchaManager {
    constructor() {
        this.captchaInstances = new Map();
    }
    
    // 创建验证码实例
    create(canvasId, options = {}) {
        const captcha = new LocalCaptcha(canvasId, options);
        this.captchaInstances.set(canvasId, captcha);
        return captcha;
    }
    
    // 获取验证码实例
    get(canvasId) {
        return this.captchaInstances.get(canvasId);
    }
    
    // 验证验证码
    validate(canvasId, inputCode) {
        const captcha = this.captchaInstances.get(canvasId);
        if (!captcha) {
            console.error(`验证码实例 ${canvasId} 不存在`);
            return false;
        }
        return captcha.validate(inputCode);
    }
    
    // 刷新验证码
    refresh(canvasId) {
        const captcha = this.captchaInstances.get(canvasId);
        if (captcha) {
            captcha.refresh();
        }
    }
    
    // 销毁验证码实例
    destroy(canvasId) {
        this.captchaInstances.delete(canvasId);
    }
}

// 全局验证码管理器实例
window.CaptchaManager = new CaptchaManager();

// 导出类（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { LocalCaptcha, CaptchaManager };
}
