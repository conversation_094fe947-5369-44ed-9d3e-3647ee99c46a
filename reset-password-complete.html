<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重置密码</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background-color: #28a745;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background-color: #218838;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .password-requirements {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 重置密码</h1>
        
        <form id="resetForm">
            <div class="form-group">
                <label for="newPassword">新密码:</label>
                <input type="password" id="newPassword" placeholder="请输入新密码" required>
                <div class="password-requirements">密码长度至少6位</div>
            </div>
            
            <div class="form-group">
                <label for="confirmPassword">确认密码:</label>
                <input type="password" id="confirmPassword" placeholder="请再次输入新密码" required>
            </div>
            
            <button type="submit" id="resetBtn">重置密码</button>
        </form>
        
        <div id="message" class="message"></div>
    </div>

    <!-- 引入 Supabase -->
    <script src="supabase-js.min.js"></script>
    <script src="js/supabase-config.js"></script>
    
    <script>
        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = text;
            messageDiv.style.display = 'block';
        }

        async function resetPassword(event) {
            event.preventDefault();
            
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const resetBtn = document.getElementById('resetBtn');

            // 验证密码
            if (newPassword.length < 6) {
                showMessage('密码长度不能少于6位', 'error');
                return;
            }

            if (newPassword !== confirmPassword) {
                showMessage('两次输入的密码不一致', 'error');
                return;
            }

            try {
                resetBtn.disabled = true;
                resetBtn.textContent = '重置中...';
                showMessage('正在重置密码...', 'info');

                // 使用 Supabase 更新密码
                const { error } = await supabase.auth.updateUser({
                    password: newPassword
                });

                if (error) {
                    throw error;
                }

                showMessage(`✅ 密码重置成功！<br><br>
                    您的新密码已设置完成。<br>
                    <a href="login.html" style="color: #007bff; text-decoration: none;">点击这里登录</a>`, 'success');

                // 3秒后自动跳转到登录页面
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 3000);

            } catch (error) {
                console.error('重置密码失败:', error);
                let errorMessage = '重置密码失败';
                
                if (error.message.includes('Invalid refresh token')) {
                    errorMessage = '重置链接已过期，请重新申请密码重置';
                } else if (error.message.includes('Password should be at least')) {
                    errorMessage = '密码不符合安全要求';
                } else {
                    errorMessage += ': ' + error.message;
                }
                
                showMessage(errorMessage, 'error');
            } finally {
                resetBtn.disabled = false;
                resetBtn.textContent = '重置密码';
            }
        }

        // 绑定表单提交事件
        document.getElementById('resetForm').addEventListener('submit', resetPassword);

        // 页面加载时检查是否有有效的重置会话
        window.addEventListener('load', function() {
            setTimeout(async () => {
                try {
                    const { data: { session } } = await supabase.auth.getSession();
                    if (!session) {
                        showMessage('❌ 无效的重置链接或链接已过期<br><a href="password-reset-sender.html" style="color: #007bff;">重新申请密码重置</a>', 'error');
                    } else {
                        showMessage('✅ 重置链接有效，请设置新密码', 'info');
                    }
                } catch (error) {
                    console.error('检查会话失败:', error);
                    showMessage('❌ 系统错误，请稍后重试', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
