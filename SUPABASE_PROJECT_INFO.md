# Supabase 项目信息备份

## 🚨 紧急情况说明
- **原因**: Supabase 账号已丢失
- **状态**: 项目仍在运行，但无法通过控制台管理
- **紧急程度**: 高 - 需要立即备份所有数据

## 📋 项目基本信息

### Supabase 项目配置
```
项目URL: https://snckktsqwrbfwtjlvcfr.supabase.co
项目引用ID: snckktsqwrbfwtjlvcfr
匿名密钥: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644
区域: 未知 (可能是 ap-southeast-1 或 us-east-1)
```

### 数据库表结构

#### 1. users 表
- **用途**: 用户管理
- **重要字段**: id, username, email, user_type, company_name, phone, is_active
- **用户类型**: guest, premium, privileged, admin
- **管理员账户**: 
  - admin (<EMAIL>) - 系统管理员
  - liangbu (<EMAIL>) - 您的管理员账户

#### 2. products 表
- **用途**: 产品信息管理
- **重要字段**: id, data_id, product_name, product_category, specifications, material, attachment_path

#### 3. customer_service_messages 表
- **用途**: 客服消息记录
- **重要字段**: id, user_id, user_name, message_content, message_type, created_at

#### 4. download_records 表
- **用途**: 下载记录追踪
- **重要字段**: id, user_id, product_id, file_type, downloaded_at

## 🔧 RLS (Row Level Security) 策略
项目使用了 RLS 策略来控制数据访问权限，这可能是导致某些查询失败的原因。

## 💾 备份建议

### 立即执行的备份步骤:
1. **使用备份工具**: 访问 `supabase-backup-tool.html`
2. **备份所有数据**: 点击"备份所有数据"按钮
3. **保存配置**: 备份 Supabase 配置信息
4. **下载文件**: 确保所有 JSON 备份文件已下载

### 备份文件清单:
- `chunsheng_complete_backup_YYYY-MM-DD.json` - 完整数据备份
- `users_backup.json` - 用户数据
- `products_backup.json` - 产品数据
- `customer_service_backup.json` - 客服数据
- `supabase_config_backup.json` - 配置信息

## 🔄 迁移方案

### 方案1: 创建新的 Supabase 项目
1. 注册新的 Supabase 账号
2. 创建新项目
3. 使用备份数据恢复表结构和数据
4. 更新项目中的 Supabase 配置

### 方案2: 迁移到其他数据库
1. PostgreSQL 自建数据库
2. Firebase Firestore
3. MongoDB Atlas
4. 其他云数据库服务

## 📁 项目文件中的 Supabase 配置位置

### 需要更新的文件:
```
js/supabase-config.js - 主配置文件
js/supabase-init.js - 初始化脚本
admin/login.html - 管理员登录页面
admin/users.html - 用户管理页面
admin/test-auth.html - 认证测试页面
所有其他包含 Supabase 配置的 HTML 文件
```

### 配置更新模板:
```javascript
const SUPABASE_URL = 'https://YOUR_NEW_PROJECT.supabase.co';
const SUPABASE_ANON_KEY = 'YOUR_NEW_ANON_KEY';
```

## ⚠️ 注意事项

1. **时间紧迫**: 项目可能随时停止工作
2. **数据完整性**: 确保所有重要数据都已备份
3. **用户影响**: 迁移期间可能影响用户访问
4. **测试**: 新环境部署后需要全面测试

## 📞 紧急联系

如果需要帮助恢复数据或迁移项目，请立即联系技术支持。

---
**备份日期**: 2025-07-16
**文档版本**: 1.0
**状态**: 紧急备份中
