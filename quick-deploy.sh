#!/bin/bash

# 快速部署脚本 - 春晟机械网站
# 目标服务器: **************

echo "=========================================="
echo "  春晟机械网站快速部署"
echo "  目标服务器: **************"
echo "=========================================="

# 服务器信息
SERVER="**************"
USER="root"
PASS="Mm.124578"

# 检查sshpass
if ! command -v sshpass &> /dev/null; then
    echo "正在安装sshpass..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install hudochenkov/sshpass/sshpass
        else
            echo "请先安装Homebrew，然后运行: brew install hudochenkov/sshpass/sshpass"
            exit 1
        fi
    elif command -v apt-get &> /dev/null; then
        # Ubuntu/Debian
        sudo apt-get update && sudo apt-get install -y sshpass
    elif command -v yum &> /dev/null; then
        # CentOS/RHEL
        sudo yum install -y sshpass
    elif command -v dnf &> /dev/null; then
        # Fedora
        sudo dnf install -y sshpass
    else
        echo "无法自动安装sshpass，请手动安装"
        exit 1
    fi
fi

echo "1. 测试服务器连接..."
if sshpass -p "$PASS" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 "$USER@$SERVER" "echo '连接成功'" 2>/dev/null; then
    echo "✅ 服务器连接正常"
else
    echo "❌ 无法连接到服务器，请检查IP和密码"
    exit 1
fi

echo ""
echo "2. 在服务器上安装Nginx..."
sshpass -p "$PASS" ssh -o StrictHostKeyChecking=no "$USER@$SERVER" << 'EOF'
    # 检测系统类型并安装nginx
    if [ -f /etc/redhat-release ]; then
        # CentOS/RHEL
        if command -v dnf &> /dev/null; then
            dnf install -y nginx
        else
            yum install -y nginx
        fi
    elif [ -f /etc/debian_version ]; then
        # Ubuntu/Debian
        apt-get update
        apt-get install -y nginx
    fi
    
    # 启动nginx
    systemctl start nginx
    systemctl enable nginx
    
    # 创建网站目录
    mkdir -p /var/www/chunsheng
    
    echo "Nginx安装完成"
EOF

echo "✅ Nginx安装完成"

echo ""
echo "3. 上传网站文件..."

# 上传文件，排除不必要的文件
sshpass -p "$PASS" rsync -avz --progress \
    --exclude='.git' \
    --exclude='node_modules' \
    --exclude='*.log' \
    --exclude='.DS_Store' \
    --exclude='deploy*.sh' \
    --exclude='upload*.sh' \
    --exclude='README*.md' \
    --exclude='*.md' \
    ./ "$USER@$SERVER:/var/www/chunsheng/"

echo "✅ 文件上传完成"

echo ""
echo "4. 配置Nginx..."
sshpass -p "$PASS" ssh -o StrictHostKeyChecking=no "$USER@$SERVER" << 'EOF'
    # 创建nginx配置
    cat > /etc/nginx/conf.d/chunsheng.conf << 'NGINXCONF'
server {
    listen 80;
    server_name **************;
    root /var/www/chunsheng;
    index index.html index.htm;

    access_log /var/log/nginx/chunsheng_access.log;
    error_log /var/log/nginx/chunsheng_error.log;

    location / {
        try_files $uri $uri/ =404;
    }

    location /admin/ {
        try_files $uri $uri/ =404;
    }

    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    client_max_body_size 20M;
}
NGINXCONF

    # 设置文件权限
    chown -R nginx:nginx /var/www/chunsheng 2>/dev/null || chown -R www-data:www-data /var/www/chunsheng
    chmod -R 755 /var/www/chunsheng
    find /var/www/chunsheng -type f -exec chmod 644 {} \;

    # 测试并重启nginx
    nginx -t && systemctl restart nginx
    
    echo "Nginx配置完成"
EOF

echo "✅ Nginx配置完成"

echo ""
echo "5. 配置防火墙..."
sshpass -p "$PASS" ssh -o StrictHostKeyChecking=no "$USER@$SERVER" << 'EOF'
    # 配置防火墙
    if command -v firewall-cmd &> /dev/null; then
        systemctl start firewalld 2>/dev/null || true
        firewall-cmd --permanent --add-service=http 2>/dev/null || true
        firewall-cmd --permanent --add-service=https 2>/dev/null || true
        firewall-cmd --reload 2>/dev/null || true
    elif command -v ufw &> /dev/null; then
        ufw --force enable
        ufw allow 80
        ufw allow 443
    fi
    
    echo "防火墙配置完成"
EOF

echo "✅ 防火墙配置完成"

echo ""
echo "6. 验证部署..."
sshpass -p "$PASS" ssh -o StrictHostKeyChecking=no "$USER@$SERVER" << 'EOF'
    echo "检查文件:"
    ls -la /var/www/chunsheng/index.html && echo "✅ index.html 存在" || echo "❌ index.html 不存在"
    ls -la /var/www/chunsheng/admin/ && echo "✅ admin目录 存在" || echo "❌ admin目录 不存在"
    
    echo ""
    echo "检查服务:"
    systemctl is-active nginx && echo "✅ Nginx 运行中" || echo "❌ Nginx 未运行"
    ss -tlnp | grep :80 && echo "✅ 端口80 监听中" || echo "❌ 端口80 未监听"
EOF

echo ""
echo "=========================================="
echo "🎉 部署完成！"
echo "=========================================="
echo ""
echo "访问地址："
echo "  主页: http://**************"
echo "  产品页面: http://**************/products.html"
echo "  管理后台: http://**************/admin/login.html"
echo ""
echo "管理员登录信息："
echo "  用户名: admin"
echo "  密码: admin123"
echo ""
echo "如果无法访问，请检查："
echo "1. 服务器防火墙是否开放80端口"
echo "2. 云服务商安全组是否开放80端口"
echo "3. 运行: ssh root@************** 'systemctl status nginx'"
echo ""
