#!/bin/bash

# 春晟机械网站部署脚本 - 专用于服务器 **************
# 自动化部署chunsheng-website项目

set -e  # 遇到错误立即退出

echo "=========================================="
echo "  春晟机械网站自动部署到 **************"
echo "=========================================="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务器配置
SERVER_IP="**************"
SSH_USER="root"
SSH_PASS="Mm.124578"
REMOTE_DIR="/var/www/chunsheng"
LOCAL_DIR="."

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要工具
check_tools() {
    log_info "检查必要工具..."
    
    if ! command -v sshpass &> /dev/null; then
        log_warning "sshpass未安装，尝试安装..."
        if command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y sshpass
        elif command -v yum &> /dev/null; then
            sudo yum install -y sshpass
        elif command -v dnf &> /dev/null; then
            sudo dnf install -y sshpass
        else
            log_error "无法自动安装sshpass，请手动安装"
            exit 1
        fi
    fi
    
    log_success "工具检查完成"
}

# 测试服务器连接
test_connection() {
    log_info "测试服务器连接..."
    
    if sshpass -p "$SSH_PASS" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 "$SSH_USER@$SERVER_IP" "echo 'Connection test successful'" 2>/dev/null; then
        log_success "服务器连接成功"
    else
        log_error "无法连接到服务器 $SERVER_IP"
        log_error "请检查："
        echo "1. 服务器IP地址是否正确"
        echo "2. 密码是否正确"
        echo "3. 服务器SSH服务是否运行"
        exit 1
    fi
}

# 在服务器上安装必要软件
install_server_software() {
    log_info "在服务器上安装必要软件..."
    
    sshpass -p "$SSH_PASS" ssh -o StrictHostKeyChecking=no "$SSH_USER@$SERVER_IP" << 'EOF'
        # 检测操作系统
        if [ -f /etc/redhat-release ]; then
            # CentOS/RHEL
            if command -v dnf &> /dev/null; then
                dnf update -y
                dnf install -y nginx firewalld
            else
                yum update -y
                yum install -y nginx firewalld
            fi
        elif [ -f /etc/debian_version ]; then
            # Ubuntu/Debian
            apt-get update
            apt-get install -y nginx ufw
        fi
        
        # 启动服务
        systemctl start nginx
        systemctl enable nginx
        
        # 配置防火墙
        if command -v firewall-cmd &> /dev/null; then
            systemctl start firewalld
            systemctl enable firewalld
            firewall-cmd --permanent --add-service=http
            firewall-cmd --permanent --add-service=https
            firewall-cmd --reload
        elif command -v ufw &> /dev/null; then
            ufw --force enable
            ufw allow 80
            ufw allow 443
        fi
        
        echo "软件安装完成"
EOF
    
    log_success "服务器软件安装完成"
}

# 创建网站目录
create_web_directory() {
    log_info "创建网站目录..."
    
    sshpass -p "$SSH_PASS" ssh -o StrictHostKeyChecking=no "$SSH_USER@$SERVER_IP" "
        mkdir -p $REMOTE_DIR
        chown -R nginx:nginx $REMOTE_DIR 2>/dev/null || chown -R www-data:www-data $REMOTE_DIR
        chmod -R 755 $REMOTE_DIR
    "
    
    log_success "网站目录创建完成"
}

# 上传网站文件
upload_files() {
    log_info "上传网站文件..."
    
    # 排除不需要的文件
    local exclude_files=(
        ".git"
        "node_modules"
        "*.log"
        ".DS_Store"
        "deploy*.sh"
        "upload*.sh"
        "README*.md"
        "*.md"
    )
    
    # 构建rsync排除参数
    local exclude_params=""
    for exclude in "${exclude_files[@]}"; do
        exclude_params="$exclude_params --exclude=$exclude"
    done
    
    # 使用rsync上传文件
    if command -v rsync &> /dev/null; then
        log_info "使用rsync上传文件..."
        sshpass -p "$SSH_PASS" rsync -avz --progress $exclude_params \
            "$LOCAL_DIR/" "$SSH_USER@$SERVER_IP:$REMOTE_DIR/"
    else
        log_info "使用scp上传文件..."
        sshpass -p "$SSH_PASS" scp -r "$LOCAL_DIR"/* "$SSH_USER@$SERVER_IP:$REMOTE_DIR/"
    fi
    
    log_success "文件上传完成"
}

# 配置Nginx
configure_nginx() {
    log_info "配置Nginx..."

    sshpass -p "$SSH_PASS" ssh -o StrictHostKeyChecking=no "$SSH_USER@$SERVER_IP" << EOF
        # 创建网站配置文件
        cat > /etc/nginx/sites-available/chunsheng 2>/dev/null || cat > /etc/nginx/conf.d/chunsheng.conf << 'NGINXCONF'
server {
    listen 80;
    server_name $SERVER_IP;
    root $REMOTE_DIR;
    index index.html index.htm;

    access_log /var/log/nginx/chunsheng_access.log;
    error_log /var/log/nginx/chunsheng_error.log;

    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 主页面
    location / {
        try_files \$uri \$uri/ =404;
    }

    # 管理后台
    location /admin/ {
        try_files \$uri \$uri/ =404;
    }

    # PDF文件特殊处理
    location ~* \.pdf$ {
        add_header Content-Type application/pdf;
        add_header Content-Disposition inline;
    }

    # 安全头部
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    server_tokens off;
    client_max_body_size 20M;
}
NGINXCONF

        # 启用站点（如果是Ubuntu/Debian）
        if [ -d /etc/nginx/sites-enabled ]; then
            ln -sf /etc/nginx/sites-available/chunsheng /etc/nginx/sites-enabled/
            rm -f /etc/nginx/sites-enabled/default
        fi

        # 测试配置
        nginx -t

        # 重启Nginx
        systemctl restart nginx

        echo "Nginx配置完成"
EOF

    log_success "Nginx配置完成"
}

# 设置文件权限
set_permissions() {
    log_info "设置文件权限..."

    sshpass -p "$SSH_PASS" ssh -o StrictHostKeyChecking=no "$SSH_USER@$SERVER_IP" "
        chown -R nginx:nginx $REMOTE_DIR 2>/dev/null || chown -R www-data:www-data $REMOTE_DIR
        find $REMOTE_DIR -type f -exec chmod 644 {} \;
        find $REMOTE_DIR -type d -exec chmod 755 {} \;
    "

    log_success "文件权限设置完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."

    # 检查服务器状态
    sshpass -p "$SSH_PASS" ssh -o StrictHostKeyChecking=no "$SSH_USER@$SERVER_IP" "
        echo '检查主要文件:'
        ls -la $REMOTE_DIR/index.html 2>/dev/null && echo '✅ index.html 存在' || echo '❌ index.html 不存在'
        ls -la $REMOTE_DIR/admin/ 2>/dev/null && echo '✅ admin目录 存在' || echo '❌ admin目录 不存在'
        ls -la $REMOTE_DIR/js/ 2>/dev/null && echo '✅ js目录 存在' || echo '❌ js目录 不存在'
        ls -la $REMOTE_DIR/css/ 2>/dev/null && echo '✅ css目录 存在' || echo '❌ css目录 不存在'

        echo ''
        echo '检查Nginx状态:'
        systemctl is-active nginx && echo '✅ Nginx 运行中' || echo '❌ Nginx 未运行'

        echo ''
        echo '检查端口监听:'
        ss -tlnp | grep :80 && echo '✅ 端口80 监听中' || echo '❌ 端口80 未监听'
    "

    # 测试HTTP访问
    log_info "测试HTTP访问..."
    if curl -s -o /dev/null -w "%{http_code}" "http://$SERVER_IP" | grep -q "200"; then
        log_success "网站访问正常"
    else
        log_warning "网站访问可能有问题，请手动检查"
    fi
}

# 显示完成信息
show_completion_info() {
    echo ""
    echo "=========================================="
    log_success "部署完成！"
    echo "=========================================="
    echo ""
    echo "部署信息："
    echo "  服务器: $SERVER_IP"
    echo "  网站目录: $REMOTE_DIR"
    echo "  部署时间: $(date)"
    echo ""
    echo "访问网站："
    echo "  主页: http://$SERVER_IP"
    echo "  产品页面: http://$SERVER_IP/products.html"
    echo "  管理后台: http://$SERVER_IP/admin/login.html"
    echo "  用户登录: http://$SERVER_IP/login.html"
    echo ""
    echo "管理后台登录信息："
    echo "  用户名: admin"
    echo "  密码: admin123"
    echo ""
    echo "常用维护命令（在服务器上执行）："
    echo "  查看网站文件: ls -la $REMOTE_DIR"
    echo "  查看Nginx状态: systemctl status nginx"
    echo "  查看访问日志: tail -f /var/log/nginx/chunsheng_access.log"
    echo "  重启Nginx: systemctl restart nginx"
    echo ""
}

# 主函数
main() {
    log_info "开始部署春晟机械网站到 $SERVER_IP"
    echo ""

    check_tools
    test_connection
    install_server_software
    create_web_directory
    upload_files
    configure_nginx
    set_permissions
    verify_deployment
    show_completion_info
}

# 运行主函数
main "$@"
