# 春晟机械网站部署脚本 - PowerShell版本
# 目标服务器: **************

param(
    [string]$ServerIP = "**************",
    [string]$Username = "root",
    [string]$Password = "Mm.124578"
)

Write-Host "==========================================" -ForegroundColor Green
Write-Host "  春晟机械网站部署到 $ServerIP" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green
Write-Host ""

# 检查是否安装了OpenSSH
$sshPath = Get-Command ssh -ErrorAction SilentlyContinue
if (-not $sshPath) {
    Write-Host "❌ 未找到SSH客户端" -ForegroundColor Red
    Write-Host "请安装OpenSSH客户端或使用Git Bash运行Linux脚本" -ForegroundColor Yellow
    Write-Host "Windows 10/11可以通过以下方式安装OpenSSH:" -ForegroundColor Yellow
    Write-Host "设置 -> 应用 -> 可选功能 -> 添加功能 -> OpenSSH客户端" -ForegroundColor Yellow
    exit 1
}

$scpPath = Get-Command scp -ErrorAction SilentlyContinue
if (-not $scpPath) {
    Write-Host "❌ 未找到SCP客户端" -ForegroundColor Red
    exit 1
}

Write-Host "✅ SSH/SCP工具检查完成" -ForegroundColor Green

# 测试连接
Write-Host ""
Write-Host "1. 测试服务器连接..." -ForegroundColor Cyan

$testConnection = @"
echo 'Connection test successful'
"@

try {
    $result = $testConnection | ssh -o StrictHostKeyChecking=no -o PasswordAuthentication=yes "$Username@$ServerIP" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 服务器连接正常" -ForegroundColor Green
    } else {
        throw "连接失败"
    }
} catch {
    Write-Host "❌ 无法连接到服务器，请检查IP和密码" -ForegroundColor Red
    Write-Host "请手动测试: ssh $Username@$ServerIP" -ForegroundColor Yellow
    exit 1
}

# 安装Nginx
Write-Host ""
Write-Host "2. 在服务器上安装Nginx..." -ForegroundColor Cyan

$installScript = @"
# 检测系统类型并安装nginx
if [ -f /etc/redhat-release ]; then
    # CentOS/RHEL
    if command -v dnf &> /dev/null; then
        dnf install -y nginx
    else
        yum install -y nginx
    fi
elif [ -f /etc/debian_version ]; then
    # Ubuntu/Debian
    apt-get update
    apt-get install -y nginx
fi

# 启动nginx
systemctl start nginx
systemctl enable nginx

# 创建网站目录
mkdir -p /var/www/chunsheng

echo 'Nginx installation completed'
"@

try {
    $installScript | ssh -o StrictHostKeyChecking=no "$Username@$ServerIP" 2>$null
    Write-Host "✅ Nginx安装完成" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Nginx安装可能有问题，继续部署..." -ForegroundColor Yellow
}

# 上传文件
Write-Host ""
Write-Host "3. 上传网站文件..." -ForegroundColor Cyan

# 获取当前目录下的所有网站文件
$filesToUpload = @(
    "*.html",
    "*.css", 
    "*.js",
    "*.jpg",
    "*.png",
    "*.gif",
    "*.pdf",
    "*.svg"
)

# 上传主要文件
foreach ($pattern in $filesToUpload) {
    $files = Get-ChildItem -Path . -Name $pattern -ErrorAction SilentlyContinue
    foreach ($file in $files) {
        try {
            scp -o StrictHostKeyChecking=no "$file" "$Username@$ServerIP:/var/www/chunsheng/" 2>$null
            Write-Host "  上传: $file" -ForegroundColor Gray
        } catch {
            Write-Host "  失败: $file" -ForegroundColor Red
        }
    }
}

# 上传目录
$directories = @("admin", "js", "css", "images", "testfile")
foreach ($dir in $directories) {
    if (Test-Path $dir) {
        try {
            scp -r -o StrictHostKeyChecking=no "$dir" "$Username@$ServerIP:/var/www/chunsheng/" 2>$null
            Write-Host "  上传目录: $dir" -ForegroundColor Gray
        } catch {
            Write-Host "  目录上传失败: $dir" -ForegroundColor Red
        }
    }
}

Write-Host "✅ 文件上传完成" -ForegroundColor Green

# 配置Nginx
Write-Host ""
Write-Host "4. 配置Nginx..." -ForegroundColor Cyan

$nginxConfig = @"
# 创建nginx配置
cat > /etc/nginx/conf.d/chunsheng.conf << 'NGINXCONF'
server {
    listen 80;
    server_name $ServerIP;
    root /var/www/chunsheng;
    index index.html index.htm;

    access_log /var/log/nginx/chunsheng_access.log;
    error_log /var/log/nginx/chunsheng_error.log;

    location / {
        try_files `$uri `$uri/ =404;
    }

    location /admin/ {
        try_files `$uri `$uri/ =404;
    }

    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|svg)`$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    client_max_body_size 20M;
}
NGINXCONF

# 设置文件权限
chown -R nginx:nginx /var/www/chunsheng 2>/dev/null || chown -R www-data:www-data /var/www/chunsheng
chmod -R 755 /var/www/chunsheng
find /var/www/chunsheng -type f -exec chmod 644 {} \;

# 测试并重启nginx
nginx -t && systemctl restart nginx

echo 'Nginx configuration completed'
"@

try {
    $nginxConfig | ssh -o StrictHostKeyChecking=no "$Username@$ServerIP" 2>$null
    Write-Host "✅ Nginx配置完成" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Nginx配置可能有问题" -ForegroundColor Yellow
}

# 配置防火墙
Write-Host ""
Write-Host "5. 配置防火墙..." -ForegroundColor Cyan

$firewallScript = @"
# 配置防火墙
if command -v firewall-cmd &> /dev/null; then
    systemctl start firewalld 2>/dev/null || true
    firewall-cmd --permanent --add-service=http 2>/dev/null || true
    firewall-cmd --permanent --add-service=https 2>/dev/null || true
    firewall-cmd --reload 2>/dev/null || true
elif command -v ufw &> /dev/null; then
    ufw --force enable
    ufw allow 80
    ufw allow 443
fi

echo 'Firewall configuration completed'
"@

try {
    $firewallScript | ssh -o StrictHostKeyChecking=no "$Username@$ServerIP" 2>$null
    Write-Host "✅ 防火墙配置完成" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 防火墙配置可能有问题" -ForegroundColor Yellow
}

# 验证部署
Write-Host ""
Write-Host "6. 验证部署..." -ForegroundColor Cyan

$verifyScript = @"
echo "检查文件:"
ls -la /var/www/chunsheng/index.html && echo "✅ index.html 存在" || echo "❌ index.html 不存在"
ls -la /var/www/chunsheng/admin/ && echo "✅ admin目录 存在" || echo "❌ admin目录 不存在"

echo ""
echo "检查服务:"
systemctl is-active nginx && echo "✅ Nginx 运行中" || echo "❌ Nginx 未运行"
ss -tlnp | grep :80 && echo "✅ 端口80 监听中" || echo "❌ 端口80 未监听"
"@

try {
    $verifyScript | ssh -o StrictHostKeyChecking=no "$Username@$ServerIP"
} catch {
    Write-Host "验证过程中出现问题" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "==========================================" -ForegroundColor Green
Write-Host "🎉 部署完成！" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green
Write-Host ""
Write-Host "访问地址：" -ForegroundColor Cyan
Write-Host "  主页: http://$ServerIP" -ForegroundColor White
Write-Host "  产品页面: http://$ServerIP/products.html" -ForegroundColor White
Write-Host "  管理后台: http://$ServerIP/admin/login.html" -ForegroundColor White
Write-Host ""
Write-Host "管理员登录信息：" -ForegroundColor Cyan
Write-Host "  用户名: admin" -ForegroundColor White
Write-Host "  密码: admin123" -ForegroundColor White
Write-Host ""
Write-Host "如果无法访问，请检查：" -ForegroundColor Yellow
Write-Host "1. 服务器防火墙是否开放80端口" -ForegroundColor White
Write-Host "2. 云服务商安全组是否开放80端口" -ForegroundColor White
Write-Host "3. 运行: ssh root@$ServerIP 'systemctl status nginx'" -ForegroundColor White
Write-Host ""
