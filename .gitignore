# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Build outputs
dist/
build/

# Temporary files
tmp/
temp/

# Backup files
*.bak
*.backup

# Downloaded files that might be temporary
*.下载

# Large test files (keep some samples but not all)
testfile/*.jpg
testfile/*.png
!testfile/A0014.jpg
!testfile/A0015.jpg
!testfile/A0016.jpg
!testfile/A0017.jpg
!testfile/A0018.jpg

# Keep PDF files as they might be important documentation
# testfile/*.pdf

# Deployment scripts (keep them but they're environment specific)
# deploy-*.sh
# *.bat
# *.ps1
