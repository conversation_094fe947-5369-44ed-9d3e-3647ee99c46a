<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品中心 - 安徽春晟机械有限公司</title>
    <meta name="description" content="安徽春晟机械有限公司产品中心，专业生产各类减震器冲压件产品">
    <meta name="keywords" content="安徽春晟机械,减震器冲压件,产品中心">

    <!-- 样式文件 -->
    <link href="pcstyle.css" rel="stylesheet" type="text/css">
    <link href="reset.css" rel="stylesheet" type="text/css">
    <link href="32_Pc_zh-CN.css" rel="stylesheet" type="text/css">
    <link href="css/custom.css" rel="stylesheet" type="text/css">
    <link href="css/customer-service-chat.css" rel="stylesheet" type="text/css">

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>

    <!-- jQuery -->
    <script src="jquery-3.6.3.min.js"></script>
    <script src="js/supabase-init.js"></script>
    <script src="js/supabase-auth.js"></script>
</head>
<body>
    <!-- 头部区域 -->
    <header class="header-section">
        <!-- Logo区域 -->
        <div class="logo-section" style="background-color: rgb(37, 37, 37); padding: 10px 0;">
            <div class="container" style="width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
                <div class="logo">
                    <a href="index.html">
                        <img src="16792504.png" alt="安徽春晟机械有限公司" style="height: 60px;">
                    </a>
                </div>
                <div class="user-actions">
                    <a href="login.html" class="login-btn" style="color: white; margin-right: 15px;">登录</a>
                    <a href="register.html" class="register-btn" style="background: #be131b; color: white; padding: 5px 15px;">注册</a>
                </div>
            </div>
        </div>

        <!-- 导航栏 -->
        <nav class="navigation" style="background-color: #F5F5F5; border-bottom: 1px solid #ddd;">
            <div class="container" style="width: 1200px; margin: 0 auto;">
                <ul class="nav-menu" style="display: flex; list-style: none; margin: 0; padding: 0;">
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="index.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">网站首页</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="about.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">关于我们</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">企业展示</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">工厂设备</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="products.html" style="display: block; padding: 20px; color: white; background: #be131b; text-decoration: none; transition: all 0.3s;">产品中心</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="news.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">新闻中心</a>
                    </li>
                    <li style="flex: 1; text-align: center;">
                        <a href="contact.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">联系我们</a>
                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- 页面标题 -->
    <section class="page-header" style="background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('16719971.jpg') center/cover; padding: 80px 0; color: white; text-align: center;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <h1 style="font-size: 48px; margin-bottom: 20px;">产品中心</h1>
            <p style="font-size: 18px;">专业生产各类减震器冲压件产品</p>
        </div>
    </section>

    <!-- 产品中心区域 -->
    <section class="product-center-section" style="padding: 80px 0; background: white;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <!-- 权限状态指示器 -->
            <div id="permission-status" style="margin-bottom: 40px; padding: 20px; background: #f8f9fa; border-radius: 10px; text-align: center; border: 1px solid #e9ecef;">
                <span style="font-size: 16px; color: #666;">当前权限：</span>
                <span id="permission-level" style="font-weight: bold; font-size: 18px;">加载中...</span>
                <div id="permission-description" style="font-size: 14px; color: #999; margin-top: 8px;"></div>
            </div>

            <div class="product-center-layout" style="display: grid; grid-template-columns: 300px 1fr; gap: 40px; min-height: 600px;">

                <!-- 左侧分类菜单 -->
                <div class="category-sidebar" style="background: #f8f9fa; border-radius: 10px; overflow: hidden; border: 1px solid #e9ecef;">
                    <!-- 分类标题 -->
                    <div class="category-header" style="background: #be131b; color: white; padding: 25px; text-align: center;">
                        <h3 style="margin: 0; font-size: 20px; font-weight: 600;">产品分类</h3>
                    </div>

                    <!-- 分类列表 -->
                    <div class="category-list" style="padding: 0;">
                        <div class="category-item active" data-category="支架（座）类" style="padding: 18px 25px; border-bottom: 1px solid #e9ecef; cursor: pointer; display: flex; align-items: center; transition: all 0.3s; background: white;">
                            <i style="margin-right: 12px; color: #be131b; font-size: 16px;">🔧</i>
                            <span style="font-size: 15px; font-weight: 500;">支架（座）类</span>
                        </div>
                        <div class="category-item" data-category="固定圈（防护套）类" style="padding: 18px 25px; border-bottom: 1px solid #e9ecef; cursor: pointer; display: flex; align-items: center; transition: all 0.3s;">
                            <i style="margin-right: 12px; color: #666; font-size: 16px;">⭕</i>
                            <span style="font-size: 15px;">固定圈（防护套）类</span>
                        </div>
                        <div class="category-item" data-category="支耳（板）类" style="padding: 18px 25px; border-bottom: 1px solid #e9ecef; cursor: pointer; display: flex; align-items: center; transition: all 0.3s;">
                            <i style="margin-right: 12px; color: #666; font-size: 16px;">📎</i>
                            <span style="font-size: 15px;">支耳（板）类</span>
                        </div>
                        <div class="category-item" data-category="弹簧盘类" style="padding: 18px 25px; border-bottom: 1px solid #e9ecef; cursor: pointer; display: flex; align-items: center; transition: all 0.3s;">
                            <i style="margin-right: 12px; color: #666; font-size: 16px;">🌀</i>
                            <span style="font-size: 15px;">弹簧盘类</span>
                        </div>
                        <div class="category-item" data-category="防尘盖（顶板）类" style="padding: 18px 25px; border-bottom: 1px solid #e9ecef; cursor: pointer; display: flex; align-items: center; transition: all 0.3s;">
                            <i style="margin-right: 12px; color: #666; font-size: 16px;">🛡️</i>
                            <span style="font-size: 15px;">防尘盖（顶板）类</span>
                        </div>
                        <div class="category-item" data-category="其它类" style="padding: 18px 25px; cursor: pointer; display: flex; align-items: center; transition: all 0.3s;">
                            <i style="margin-right: 12px; color: #666; font-size: 16px;">📦</i>
                            <span style="font-size: 15px;">其它类</span>
                        </div>
                    </div>

                    <!-- 筛选功能 -->
                    <div style="padding: 25px; border-top: 2px solid #e9ecef; background: white;">
                        <h4 style="margin: 0 0 20px 0; color: #333; font-size: 18px; font-weight: 600;">快速筛选</h4>

                        <div style="margin-bottom: 18px;">
                            <label style="display: block; margin-bottom: 8px; color: #333; font-weight: 500; font-size: 14px;">品牌：</label>
                            <input type="text" id="brand-filter" placeholder="如：奥迪、宝马、大众..." style="width: 100%; padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; box-sizing: border-box;">
                        </div>

                        <div style="margin-bottom: 18px;">
                            <label style="display: block; margin-bottom: 8px; color: #333; font-weight: 500; font-size: 14px;">车型：</label>
                            <input type="text" id="model-filter" placeholder="如：A4L、3系、C级..." style="width: 100%; padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; box-sizing: border-box;">
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; color: #333; font-weight: 500; font-size: 14px;">产品：</label>
                            <input type="text" id="product-filter" placeholder="如：减震器、支架、CS0001..." style="width: 100%; padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; box-sizing: border-box;">
                        </div>

                        <div style="text-align: center; margin-top: 20px;">
                            <button onclick="applyHomeFilters()" style="background: #be131b; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 14px; margin-right: 12px; transition: background 0.3s; font-weight: 500;" onmouseover="this.style.background='#a01018'" onmouseout="this.style.background='#be131b'">
                                查询
                            </button>
                            <button onclick="resetHomeFilters()" style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 14px; transition: background 0.3s; font-weight: 500;" onmouseover="this.style.background='#5a6268'" onmouseout="this.style.background='#6c757d'">
                                重置
                            </button>
                        </div>

                        <!-- 搜索状态指示器 -->
                        <div id="search-status" style="display: none; margin-top: 18px; padding: 12px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 6px; color: #155724; font-size: 13px; text-align: center;">
                            正在搜索...
                        </div>
                    </div>
                </div>

                <!-- 右侧产品展示区域 -->
                <div class="product-display" style="background: #f8f9fa; border-radius: 10px; padding: 40px; border: 1px solid #e9ecef;">
                    <div id="featured-products" class="featured-products" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); gap: 25px; margin-bottom: 40px;">
                        <!-- 产品将通过JavaScript动态加载 -->
                    </div>
                    <div class="text-center" style="text-align: center; padding-top: 20px; border-top: 1px solid #e9ecef;">
                        <a href="#" id="advanced-search-link" onclick="checkAdvancedSearchPermission()" style="background: #be131b; color: white; padding: 15px 35px; text-decoration: none; border-radius: 6px; font-size: 16px; font-weight: 500; transition: background 0.3s; display: inline-block;" onmouseover="this.style.background='#a01018'" onmouseout="this.style.background='#be131b'">高级搜索</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer-section" style="background: #333; color: white; padding: 60px 0 40px;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <div class="footer-content" style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 40px; margin-bottom: 40px;">
                <div class="footer-info">
                    <h3 style="color: white; margin-bottom: 20px; font-size: 20px;">安徽春晟机械有限公司</h3>
                    <p style="line-height: 1.6; color: #ccc; margin-bottom: 10px;">专业从事减震器冲压件设计与生产的企业</p>
                    <p style="line-height: 1.6; color: #ccc; margin-bottom: 10px;">地址：安徽省广德经济开发区</p>
                    <p style="line-height: 1.6; color: #ccc;">交通便利，环境优雅</p>
                </div>
                <div class="footer-links">
                    <h3 style="color: white; margin-bottom: 20px; font-size: 18px;">快速链接</h3>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 10px;"><a href="about.html" style="color: #ccc; text-decoration: none; transition: color 0.3s;">关于我们</a></li>
                        <li style="margin-bottom: 10px;"><a href="products.html" style="color: #ccc; text-decoration: none; transition: color 0.3s;">产品中心</a></li>
                        <li style="margin-bottom: 10px;"><a href="factory.html" style="color: #ccc; text-decoration: none; transition: color 0.3s;">企业展示</a></li>
                        <li style="margin-bottom: 10px;"><a href="contact.html" style="color: #ccc; text-decoration: none; transition: color 0.3s;">联系我们</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3 style="color: white; margin-bottom: 20px; font-size: 18px;">联系方式</h3>
                    <p style="line-height: 1.6; color: #ccc; margin-bottom: 10px;">📍 安徽省广德经济开发区</p>
                    <p style="line-height: 1.6; color: #ccc; margin-bottom: 10px;">📞 联系电话：待更新</p>
                    <p style="line-height: 1.6; color: #ccc;">📧 邮箱：待更新</p>
                </div>
            </div>
            <div class="footer-bottom" style="border-top: 1px solid #555; padding-top: 30px; text-align: center;">
                <p style="color: #999; margin: 0;">&copy; 2024 安徽春晟机械有限公司. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <!-- 产品中心样式 -->
    <style>
        .category-item {
            transition: all 0.3s ease;
        }

        .category-item:hover {
            background: #f8f9fa;
            transform: translateX(5px);
        }

        .category-item.active {
            background: #fff3cd;
            border-left: 4px solid #be131b;
        }

        .category-item.active span {
            color: #be131b;
            font-weight: bold;
        }

        .category-item.active i {
            color: #be131b !important;
        }

        .product-item {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .product-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .product-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }

        .product-item .product-info {
            padding: 15px;
        }

        .product-item .product-name {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
            text-align: center;
        }

        .product-item .product-code {
            font-size: 12px;
            color: #666;
            text-align: center;
        }

        @media (max-width: 768px) {
            .product-center-layout {
                flex-direction: column !important;
            }

            .category-sidebar {
                width: 100% !important;
                margin-bottom: 20px;
            }

            .product-display {
                padding: 20px !important;
            }

            .container {
                width: 95% !important;
            }
        }
    </style>

    <script>
        // 产品中心功能
        let allProducts = [];
        let currentCategory = '支架（座）类';

        // 产品分类映射（数据库代码 -> 中文名称）
        const categoryMapping = {
            'ZJ': '支架（座）类',
            'GD': '固定圈（防护套）类',
            'ZE': '支耳（板）类',
            'TP': '弹簧盘类',
            'FC': '防尘盖（顶板）类',
            'QT': '其它类'
        };

        // 反向映射（中文名称 -> 数据库代码数组）
        const reverseCategoryMapping = {
            '支架（座）类': ['ZJ'],
            '固定圈（防护套）类': ['GD'],
            '支耳（板）类': ['ZE'],
            '弹簧盘类': ['TP'],
            '防尘盖（顶板）类': ['FC'],
            '其它类': ['QT']
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟初始化，确保权限系统已加载
            setTimeout(() => {
                initializeProductCenter();
                // 强制刷新权限状态
                forceRefreshPermissionStatus();
            }, 1000);
        });

        // 监听权限状态变化
        window.addEventListener('authReady', function(event) {
            console.log('权限系统已就绪，更新产品显示');
            setTimeout(() => {
                updatePermissionStatus();
                displayProductsByCategory(currentCategory);
            }, 500); // 延迟一点确保认证系统完全就绪
        });

        // 监听用户登录状态变化
        setInterval(() => {
            const currentDisplayedType = document.getElementById('permission-level')?.textContent || '';
            const actualBadgeText = getPermissionBadgeText(getCurrentUserType());

            if (currentDisplayedType !== actualBadgeText) {
                console.log('检测到权限状态变化，更新显示', {
                    displayed: currentDisplayedType,
                    actual: actualBadgeText
                });
                updatePermissionStatus();
                displayProductsByCategory(currentCategory);
            }
        }, 2000);

        // 初始化产品中心
        async function initializeProductCenter() {
            try {
                // 更新权限状态显示
                updatePermissionStatus();

                // 加载产品数据
                await loadProducts();

                // 初始化分类切换功能
                initializeCategorySwitch();

                // 显示默认分类的产品
                displayProductsByCategory(currentCategory);

            } catch (error) {
                console.error('初始化产品中心失败:', error);
            }
        }

        // 更新权限状态显示
        function updatePermissionStatus() {
            const userType = getCurrentUserType();
            const permissionLevel = document.getElementById('permission-level');
            const permissionDescription = document.getElementById('permission-description');

            if (permissionLevel && permissionDescription) {
                const badgeText = getPermissionBadgeText(userType);
                const badgeColor = getPermissionBadgeColor(userType);

                permissionLevel.innerHTML = `<span style="color: ${badgeColor};">${badgeText}</span>`;

                let description = '';
                switch(userType) {
                    case 'admin':
                        description = '拥有完整系统权限，可查看详情、下载所有资料';
                        break;
                    case 'privileged':
                        description = '可查看详情、下载基础资料和PDF文档，可访问产品中心高级搜索';
                        break;
                    case 'premium':
                        description = '可查看产品详情和下载基础资料';
                        break;
                    case 'normal':
                        description = '可查看产品基础详情，管理个人信息';
                        break;
                    case 'guest':
                    default:
                        description = '可浏览基本产品信息，登录后可查看更多详情';
                        break;
                }

                permissionDescription.textContent = description;
            }

            if (typeof currentCategory !== 'undefined') {
                displayProductsByCategory(currentCategory);
            }
        }

        // 强制刷新权限状态
        function forceRefreshPermissionStatus() {
            let userType = 'guest';
            let userInfo = null;

            if (window.auth && window.auth.isInitialized) {
                userType = window.auth.getUserType();
                userInfo = window.auth.getCurrentUser();
            }

            if (userType === 'guest') {
                try {
                    const savedUser = localStorage.getItem('simple_auth_user');
                    const loginTime = localStorage.getItem('simple_auth_login_time');

                    if (savedUser && loginTime) {
                        const now = Date.now();
                        const loginTimestamp = parseInt(loginTime);
                        const expireTime = 24 * 60 * 60 * 1000;

                        if (now - loginTimestamp < expireTime) {
                            const user = JSON.parse(savedUser);
                            userType = user.user_type || 'premium';
                            userInfo = user;

                            window.currentUser = user;
                            window.currentUserType = userType;
                        }
                    }
                } catch (e) {
                    console.error('读取本地存储失败:', e);
                }
            }

            const permissionLevel = document.getElementById('permission-level');
            const permissionDescription = document.getElementById('permission-description');

            if (permissionLevel && permissionDescription) {
                const badgeText = getPermissionBadgeText(userType);
                const badgeColor = getPermissionBadgeColor(userType);

                permissionLevel.innerHTML = `<span style="color: ${badgeColor};">${badgeText}</span>`;

                let description = '';
                switch(userType) {
                    case 'admin':
                        description = '拥有完整系统权限，可查看详情、下载所有资料';
                        break;
                    case 'privileged':
                        description = '可查看详情、下载基础资料和PDF文档，可访问产品中心高级搜索';
                        break;
                    case 'premium':
                        description = '可查看产品详情、下载基础资料和PDF文档';
                        break;
                    case 'normal':
                        description = '可查看产品基础详情，管理个人信息';
                        break;
                    case 'guest':
                    default:
                        description = '可浏览基本产品信息，登录后可查看更多详情';
                        break;
                }

                permissionDescription.textContent = description;
            }

            if (typeof currentCategory !== 'undefined') {
                displayProductsByCategory(currentCategory);
            }
        }

        // 加载产品数据
        async function loadProducts() {
            try {
                const { data: products, error } = await supabase
                    .from('products')
                    .select('*')
                    .order('created_at', { ascending: false });

                if (error) {
                    console.error('加载产品失败:', error);
                    return;
                }

                allProducts = products || [];
                console.log('加载了', allProducts.length, '个产品');

            } catch (error) {
                console.error('加载产品数据失败:', error);
            }
        }

        // 初始化分类切换功能
        function initializeCategorySwitch() {
            const categoryItems = document.querySelectorAll('.category-item');
            categoryItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 移除所有active类
                    categoryItems.forEach(i => i.classList.remove('active'));
                    // 添加active类到当前项
                    this.classList.add('active');

                    // 获取选中的分类
                    const selectedCategory = this.getAttribute('data-category');
                    currentCategory = selectedCategory;

                    // 显示对应分类的产品
                    displayProductsByCategory(selectedCategory);
                });
            });
        }

        // 检查高级搜索权限
        function checkAdvancedSearchPermission() {
            const userType = getCurrentUserType();

            if (userType === 'guest' || userType === 'normal') {
                alert('您需要高级用户权限才能访问高级搜索功能。请联系管理员升级权限。');
                return false;
            }

            // 有权限的用户跳转到高级搜索页面
            window.location.href = 'advanced-search.html';
            return true;
        }

        // 根据分类显示产品
        function displayProductsByCategory(category) {
            const container = document.getElementById('featured-products');

            const categoryCodes = reverseCategoryMapping[category] || [];

            const filteredProducts = allProducts.filter(product => {
                return categoryCodes.includes(product.product_category);
            });

            if (filteredProducts.length === 0) {
                container.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #666;">
                        <p style="font-size: 16px;">暂无${category}产品</p>
                        <a href="#" onclick="checkAdvancedSearchPermission()" style="color: #be131b; text-decoration: none;">查看高级搜索 →</a>
                    </div>
                `;
                return;
            }

            const productsToShow = filteredProducts.slice(0, 8);

            container.innerHTML = productsToShow.map(product =>
                generateProductHTML(product)
            ).join('');
        }

        // 根据用户权限生成产品HTML
        function generateProductHTML(product) {
            const userType = getCurrentUserType();

            let productHTML = `
                <div class="product-item" onclick="handleProductClick('${product.id}')" style="border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden; transition: all 0.3s; cursor: pointer; position: relative;">
                    <div class="product-image" style="height: 150px; overflow: hidden; position: relative;">
                        <img src="${product.product_image || 'placeholder.svg'}"
                             alt="${product.product_name}"
                             onerror="this.src='placeholder.svg'"
                             style="width: 100%; height: 100%; object-fit: cover;">

                        <div class="permission-badge" style="position: absolute; top: 8px; right: 8px; background: ${getPermissionBadgeColor(userType)}; color: white; padding: 2px 6px; border-radius: 4px; font-size: 10px;">
                            ${getPermissionBadgeText(userType)}
                        </div>
                    </div>

                    <div class="product-info" style="padding: 12px;">
                        <div class="product-name" style="font-weight: bold; margin-bottom: 4px; font-size: 14px;">${product.product_name}</div>
                        <div class="product-code" style="color: #666; font-size: 12px; margin-bottom: 8px;">${product.stock_code || ''}</div>
                    </div>
                </div>
            `;

            return productHTML;
        }

        // 处理产品卡片点击事件
        function handleProductClick(productId) {
            const userType = getCurrentUserType();

            if (userType === 'guest') {
                if (confirm('您需要登录后才能查看产品详情。是否前往登录页面？')) {
                    window.location.href = 'login.html';
                }
            } else {
                window.location.href = `product-detail.html?id=${productId}`;
            }
        }

        // 权限检查函数
        function getCurrentUserType() {
            if (window.auth && window.auth.isInitialized) {
                return window.auth.getUserType();
            }

            try {
                const savedUser = localStorage.getItem('simple_auth_user');
                const loginTime = localStorage.getItem('simple_auth_login_time');

                if (savedUser && loginTime) {
                    const now = Date.now();
                    const loginTimestamp = parseInt(loginTime);
                    const expireTime = 24 * 60 * 60 * 1000;

                    if (now - loginTimestamp < expireTime) {
                        const user = JSON.parse(savedUser);
                        return user.user_type || 'premium';
                    }
                }
            } catch (e) {
                console.error('读取本地存储失败:', e);
            }

            return 'guest';
        }

        function getPermissionBadgeText(userType) {
            switch(userType) {
                case 'admin': return '管理员';
                case 'privileged': return '特许用户';
                case 'premium': return '高级用户';
                case 'normal': return '注册用户';
                case 'guest':
                default: return '游客';
            }
        }

        function getPermissionBadgeColor(userType) {
            switch(userType) {
                case 'admin': return '#dc3545';
                case 'privileged': return '#fd7e14';
                case 'premium': return '#ffc107';
                case 'normal': return '#28a745';
                case 'guest':
                default: return '#6c757d';
            }
        }

        // 应用首页筛选
        function applyHomeFilters() {
            const brandFilter = document.getElementById('brand-filter').value.trim().toLowerCase();
            const modelFilter = document.getElementById('model-filter').value.trim().toLowerCase();
            const productFilter = document.getElementById('product-filter').value.trim().toLowerCase();

            const searchStatus = document.getElementById('search-status');
            if (searchStatus && (brandFilter || modelFilter || productFilter)) {
                searchStatus.style.display = 'block';
            }

            if (!brandFilter && !modelFilter && !productFilter) {
                if (searchStatus) searchStatus.style.display = 'none';
                displayProductsByCategory(currentCategory);
                return;
            }

            const container = document.getElementById('featured-products');

            const filteredProducts = allProducts.filter(product => {
                let categoryMatch = true;
                if (!brandFilter && !modelFilter && !productFilter) {
                    const categoryCodes = reverseCategoryMapping[currentCategory] || [];
                    categoryMatch = categoryCodes.includes(product.product_category);
                }

                const brandMatch = !brandFilter ||
                    (product.car_models && product.car_models.toLowerCase().includes(brandFilter));

                const modelMatch = !modelFilter ||
                    (product.car_models && product.car_models.toLowerCase().includes(modelFilter));

                const productMatch = !productFilter ||
                    product.product_name.toLowerCase().includes(productFilter) ||
                    (product.stock_code && product.stock_code.toLowerCase().includes(productFilter)) ||
                    (product.data_id && product.data_id.toLowerCase().includes(productFilter));

                return categoryMatch && brandMatch && modelMatch && productMatch;
            });

            if (filteredProducts.length === 0) {
                container.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #666;">
                        <p style="font-size: 16px;">没有找到符合条件的产品</p>
                        <p style="font-size: 14px; color: #999;">请尝试调整筛选条件</p>
                        <button onclick="resetHomeFilters()" style="background: #be131b; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 10px;">重置筛选</button>
                    </div>
                `;
                return;
            }

            const productsToShow = filteredProducts.slice(0, 8);

            container.innerHTML = productsToShow.map(product =>
                generateProductHTML(product)
            ).join('');

            if (searchStatus) searchStatus.style.display = 'none';
        }

        // 重置首页筛选
        function resetHomeFilters() {
            document.getElementById('brand-filter').value = '';
            document.getElementById('model-filter').value = '';
            document.getElementById('product-filter').value = '';

            const searchStatus = document.getElementById('search-status');
            if (searchStatus) searchStatus.style.display = 'none';

            displayProductsByCategory(currentCategory);
        }

        // 为筛选输入框添加回车键支持
        document.addEventListener('DOMContentLoaded', function() {
            const filterInputs = ['brand-filter', 'model-filter', 'product-filter'];

            filterInputs.forEach(inputId => {
                const input = document.getElementById(inputId);
                if (input) {
                    input.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            applyHomeFilters();
                        }
                    });
                }
            });
        });
    </script>

    <!-- 客服系统脚本 -->
    <script src="js/customer-service-chat.js"></script>
</body>
</html>