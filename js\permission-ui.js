// 权限UI组件 - 三级权限系统界面更新
class PermissionUI {
    constructor() {
        this.authManager = window.authManager;
        this.init();
    }

    init() {
        // 等待认证管理器初始化完成
        if (this.authManager) {
            this.setupPermissionDisplay();
            this.setupPermissionHints();
            this.updateProductAccess();
        } else {
            // 延迟初始化
            setTimeout(() => this.init(), 1000);
        }
    }

    // 设置权限显示
    setupPermissionDisplay() {
        // 创建权限状态栏
        this.createPermissionStatusBar();
        
        // 更新导航菜单
        this.updateNavigationMenu();
        
        // 设置权限提示
        this.setupPermissionTooltips();
    }

    // 创建权限状态栏
    createPermissionStatusBar() {
        const statusBar = document.createElement('div');
        statusBar.id = 'permission-status-bar';
        statusBar.className = 'permission-status-bar';
        statusBar.innerHTML = this.getPermissionStatusHTML();
        
        // 插入到页面顶部
        const header = document.querySelector('header, .header-section');
        if (header) {
            header.appendChild(statusBar);
        } else {
            document.body.insertBefore(statusBar, document.body.firstChild);
        }

        // 添加样式
        this.addPermissionStyles();
    }

    // 获取权限状态HTML
    getPermissionStatusHTML() {
        if (!this.authManager.isLoggedIn()) {
            return `
                <div class="permission-info guest">
                    <span class="permission-icon">👤</span>
                    <span class="permission-text">游客模式 - 基础浏览权限</span>
                    <a href="login.html" class="permission-action">登录</a>
                    <a href="register.html" class="permission-action">注册</a>
                </div>
            `;
        }

        const userType = this.authManager.getUserType();
        const userTypeDisplay = this.authManager.getUserTypeDisplay();
        const description = this.authManager.getUserPermissionDescription();
        
        const icons = {
            normal: '👤',
            premium: '⭐',
            privileged: '💎',
            admin: '👑'
        };

        return `
            <div class="permission-info ${userType}">
                <span class="permission-icon">${icons[userType] || '👤'}</span>
                <span class="permission-text">${userTypeDisplay} - ${description}</span>
                <button class="permission-action" onclick="this.showPermissionDetails()">权限详情</button>
                <button class="permission-action logout" onclick="window.authManager.logout()">退出</button>
            </div>
        `;
    }

    // 添加权限样式
    addPermissionStyles() {
        if (document.getElementById('permission-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'permission-styles';
        styles.textContent = `
            .permission-status-bar {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 8px 20px;
                font-size: 14px;
                position: sticky;
                top: 0;
                z-index: 1000;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }

            .permission-info {
                display: flex;
                align-items: center;
                justify-content: space-between;
                max-width: 1200px;
                margin: 0 auto;
            }

            .permission-icon {
                font-size: 18px;
                margin-right: 10px;
            }

            .permission-text {
                flex: 1;
                font-weight: 500;
            }

            .permission-action {
                background: rgba(255,255,255,0.2);
                color: white;
                border: 1px solid rgba(255,255,255,0.3);
                padding: 5px 12px;
                border-radius: 15px;
                text-decoration: none;
                margin-left: 10px;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .permission-action:hover {
                background: rgba(255,255,255,0.3);
                transform: translateY(-1px);
            }

            .permission-action.logout {
                background: rgba(220, 53, 69, 0.8);
                border-color: rgba(220, 53, 69, 0.8);
            }

            .permission-tooltip {
                position: relative;
                cursor: help;
            }

            .permission-tooltip::after {
                content: attr(data-tooltip);
                position: absolute;
                bottom: 100%;
                left: 50%;
                transform: translateX(-50%);
                background: #333;
                color: white;
                padding: 8px 12px;
                border-radius: 5px;
                font-size: 12px;
                white-space: nowrap;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
                z-index: 1001;
            }

            .permission-tooltip:hover::after {
                opacity: 1;
                visibility: visible;
            }

            .access-restricted {
                opacity: 0.5;
                pointer-events: none;
                position: relative;
            }

            .access-restricted::before {
                content: "🔒 需要更高权限";
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 5px 10px;
                border-radius: 5px;
                font-size: 12px;
                z-index: 10;
            }

            @media (max-width: 768px) {
                .permission-status-bar {
                    padding: 5px 10px;
                    font-size: 12px;
                }
                
                .permission-info {
                    flex-direction: column;
                    gap: 5px;
                }
                
                .permission-action {
                    font-size: 11px;
                    padding: 3px 8px;
                }
            }
        `;
        document.head.appendChild(styles);
    }

    // 更新导航菜单
    updateNavigationMenu() {
        const menuItems = document.querySelectorAll('.nav-item, .menu-item');
        menuItems.forEach(item => {
            const requiredPermission = item.dataset.permission;
            if (requiredPermission) {
                const hasPermission = this.checkPermission(requiredPermission);
                item.style.display = hasPermission ? 'block' : 'none';
            }
        });
    }

    // 检查权限
    checkPermission(permission) {
        switch (permission) {
            case 'view_details':
                return this.authManager.canViewDetails();
            case 'download_basic':
                return this.authManager.canDownloadBasic();
            case 'download_all':
                return this.authManager.canDownloadAll();
            case 'admin':
                return this.authManager.canAccessAdmin();
            default:
                return true;
        }
    }

    // 设置权限提示
    setupPermissionTooltips() {
        const elements = document.querySelectorAll('[data-permission-required]');
        elements.forEach(element => {
            const requiredPermission = element.dataset.permissionRequired;
            const hasPermission = this.checkPermission(requiredPermission);
            
            if (!hasPermission) {
                element.classList.add('access-restricted');
                element.setAttribute('data-tooltip', this.getPermissionTooltip(requiredPermission));
                element.classList.add('permission-tooltip');
            }
        });
    }

    // 获取权限提示文本
    getPermissionTooltip(permission) {
        const tooltips = {
            view_details: '需要高级用户权限才能查看详情',
            download_basic: '需要特许用户权限才能下载资料',
            download_all: '需要管理员权限才能下载所有资料',
            admin: '需要管理员权限才能访问'
        };
        return tooltips[permission] || '权限不足';
    }

    // 更新产品访问权限
    updateProductAccess() {
        const productItems = document.querySelectorAll('.product-item, .product-card');
        productItems.forEach(item => {
            this.updateProductItemAccess(item);
        });
    }

    // 更新单个产品项的访问权限
    updateProductItemAccess(productItem) {
        const detailsBtn = productItem.querySelector('.view-details-btn, .product-details');
        const downloadBtn = productItem.querySelector('.download-btn');
        
        // 详情查看权限
        if (detailsBtn) {
            if (this.authManager.canViewDetails()) {
                detailsBtn.style.display = 'block';
                detailsBtn.classList.remove('access-restricted');
            } else {
                detailsBtn.style.display = 'none';
            }
        }

        // 下载权限
        if (downloadBtn) {
            const downloadLevel = downloadBtn.dataset.level || 'basic';
            const canDownload = downloadLevel === 'all' ? 
                this.authManager.canDownloadAll() : 
                this.authManager.canDownloadBasic();
                
            if (canDownload) {
                downloadBtn.style.display = 'block';
                downloadBtn.classList.remove('access-restricted');
            } else {
                downloadBtn.style.display = 'none';
            }
        }
    }

    // 显示权限详情模态框
    showPermissionDetails() {
        const modal = document.createElement('div');
        modal.className = 'permission-modal';
        modal.innerHTML = `
            <div class="permission-modal-content">
                <div class="permission-modal-header">
                    <h3>权限等级说明</h3>
                    <button class="permission-modal-close">&times;</button>
                </div>
                <div class="permission-modal-body">
                    ${this.getPermissionDetailsHTML()}
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 关闭模态框
        modal.querySelector('.permission-modal-close').onclick = () => {
            document.body.removeChild(modal);
        };
        
        modal.onclick = (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        };
    }

    // 获取权限详情HTML
    getPermissionDetailsHTML() {
        return `
            <div class="permission-levels">
                <div class="permission-level">
                    <div class="permission-level-icon">👤</div>
                    <div class="permission-level-info">
                        <h4>游客</h4>
                        <p>• 浏览产品基本信息</p>
                        <p>• 发送客服消息</p>
                        <p>• 无需注册即可使用</p>
                    </div>
                </div>

                <div class="permission-level">
                    <div class="permission-level-icon">👤</div>
                    <div class="permission-level-info">
                        <h4>普通用户</h4>
                        <p>• 查看产品基础详情</p>
                        <p>• 管理个人信息</p>
                        <p>• 发送客服消息</p>
                        <p>• 新注册用户默认权限</p>
                    </div>
                </div>

                <div class="permission-level">
                    <div class="permission-level-icon">⭐</div>
                    <div class="permission-level-info">
                        <h4>高级用户</h4>
                        <p>• 查看产品详细信息</p>
                        <p>• 下载基础技术资料</p>
                        <p>• 查看技术规格</p>
                        <p>• 需要申请或升级获得</p>
                    </div>
                </div>

                <div class="permission-level">
                    <div class="permission-level-icon">💎</div>
                    <div class="permission-level-info">
                        <h4>特许用户</h4>
                        <p>• 下载PDF技术资料</p>
                        <p>• 访问产品中心高级搜索</p>
                        <p>• 查看详细工艺信息</p>
                        <p>• 需要管理员审核</p>
                    </div>
                </div>

                <div class="permission-level">
                    <div class="permission-level-icon">👑</div>
                    <div class="permission-level-info">
                        <h4>管理员</h4>
                        <p>• 完整系统管理权限</p>
                        <p>• 用户管理</p>
                        <p>• 产品管理</p>
                        <p>• 下载所有资料</p>
                    </div>
                </div>
            </div>
        `;
    }

    // 刷新权限显示
    refresh() {
        const statusBar = document.getElementById('permission-status-bar');
        if (statusBar) {
            statusBar.innerHTML = this.getPermissionStatusHTML();
        }
        this.updateNavigationMenu();
        this.setupPermissionTooltips();
        this.updateProductAccess();
    }
}

// 初始化权限UI
document.addEventListener('DOMContentLoaded', () => {
    window.permissionUI = new PermissionUI();
});

// 导出给其他模块使用
window.PermissionUI = PermissionUI;
