#!/bin/bash

# 春晟机械网站交互式部署脚本
# 目标服务器: **************

echo "=========================================="
echo "  春晟机械网站部署到 **************"
echo "=========================================="
echo ""

# 服务器信息
SERVER="**************"
USER="root"
REMOTE_DIR="/var/www/chunsheng"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "服务器信息："
echo "  IP: $SERVER"
echo "  用户: $USER"
echo "  密码: Mm.124578"
echo ""
echo "请确保您可以SSH连接到服务器"
echo "如果是首次连接，请输入 'yes' 接受服务器指纹"
echo ""

# 测试连接
log_info "1. 测试服务器连接..."
if ssh -o ConnectTimeout=10 "$USER@$SERVER" "echo '连接成功'" 2>/dev/null; then
    log_success "服务器连接正常"
else
    log_error "无法连接到服务器"
    echo "请手动测试连接: ssh $USER@$SERVER"
    exit 1
fi

# 安装Nginx
log_info "2. 在服务器上安装Nginx..."
ssh "$USER@$SERVER" << 'EOF'
    echo "检测系统并安装Nginx..."
    
    # 检测系统类型
    if [ -f /etc/redhat-release ]; then
        echo "检测到CentOS/RHEL系统"
        if command -v dnf &> /dev/null; then
            dnf install -y nginx
        else
            yum install -y nginx
        fi
    elif [ -f /etc/debian_version ]; then
        echo "检测到Ubuntu/Debian系统"
        apt-get update
        apt-get install -y nginx
    else
        echo "未知系统类型，尝试通用安装..."
        if command -v dnf &> /dev/null; then
            dnf install -y nginx
        elif command -v yum &> /dev/null; then
            yum install -y nginx
        elif command -v apt-get &> /dev/null; then
            apt-get update && apt-get install -y nginx
        fi
    fi
    
    # 启动Nginx
    systemctl start nginx
    systemctl enable nginx
    
    # 创建网站目录
    mkdir -p /var/www/chunsheng
    
    echo "Nginx安装完成"
EOF

if [ $? -eq 0 ]; then
    log_success "Nginx安装完成"
else
    log_warning "Nginx安装可能有问题，继续部署..."
fi

# 创建文件列表，排除不需要的文件
log_info "3. 准备上传文件..."
find . -type f \( \
    -name "*.html" -o \
    -name "*.css" -o \
    -name "*.js" -o \
    -name "*.jpg" -o \
    -name "*.jpeg" -o \
    -name "*.png" -o \
    -name "*.gif" -o \
    -name "*.svg" -o \
    -name "*.pdf" -o \
    -name "*.ico" \
\) ! -path "./.git/*" ! -name "deploy*" ! -name "upload*" > /tmp/files_to_upload.txt

echo "找到 $(wc -l < /tmp/files_to_upload.txt) 个文件需要上传"

# 上传文件
log_info "4. 上传网站文件..."
echo "正在上传文件，请稍候..."

# 使用rsync上传文件（如果可用）
if command -v rsync &> /dev/null; then
    rsync -avz --progress \
        --include="*.html" \
        --include="*.css" \
        --include="*.js" \
        --include="*.jpg" \
        --include="*.jpeg" \
        --include="*.png" \
        --include="*.gif" \
        --include="*.svg" \
        --include="*.pdf" \
        --include="*.ico" \
        --include="admin/***" \
        --include="js/***" \
        --include="css/***" \
        --include="images/***" \
        --include="testfile/***" \
        --exclude="*" \
        ./ "$USER@$SERVER:$REMOTE_DIR/"
else
    # 使用scp上传主要文件
    scp *.html "$USER@$SERVER:$REMOTE_DIR/" 2>/dev/null || true
    scp *.css "$USER@$SERVER:$REMOTE_DIR/" 2>/dev/null || true
    scp *.js "$USER@$SERVER:$REMOTE_DIR/" 2>/dev/null || true
    scp *.jpg *.jpeg *.png *.gif *.svg *.ico "$USER@$SERVER:$REMOTE_DIR/" 2>/dev/null || true
    
    # 上传目录
    for dir in admin js css images testfile; do
        if [ -d "$dir" ]; then
            echo "上传目录: $dir"
            scp -r "$dir" "$USER@$SERVER:$REMOTE_DIR/" 2>/dev/null || true
        fi
    done
fi

log_success "文件上传完成"

# 配置Nginx
log_info "5. 配置Nginx..."
ssh "$USER@$SERVER" << EOF
    # 创建Nginx配置文件
    cat > /etc/nginx/conf.d/chunsheng.conf << 'NGINXCONF'
server {
    listen 80;
    server_name $SERVER;
    root $REMOTE_DIR;
    index index.html index.htm;

    access_log /var/log/nginx/chunsheng_access.log;
    error_log /var/log/nginx/chunsheng_error.log;

    location / {
        try_files \$uri \$uri/ =404;
    }

    location /admin/ {
        try_files \$uri \$uri/ =404;
    }

    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|svg)\$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    client_max_body_size 20M;
}
NGINXCONF

    # 设置文件权限
    chown -R nginx:nginx $REMOTE_DIR 2>/dev/null || chown -R www-data:www-data $REMOTE_DIR
    chmod -R 755 $REMOTE_DIR
    find $REMOTE_DIR -type f -exec chmod 644 {} \;

    # 测试并重启Nginx
    nginx -t && systemctl restart nginx
    
    echo "Nginx配置完成"
EOF

if [ $? -eq 0 ]; then
    log_success "Nginx配置完成"
else
    log_warning "Nginx配置可能有问题"
fi

# 配置防火墙
log_info "6. 配置防火墙..."
ssh "$USER@$SERVER" << 'EOF'
    # 配置防火墙
    if command -v firewall-cmd &> /dev/null; then
        echo "配置firewalld..."
        systemctl start firewalld 2>/dev/null || true
        firewall-cmd --permanent --add-service=http 2>/dev/null || true
        firewall-cmd --permanent --add-service=https 2>/dev/null || true
        firewall-cmd --reload 2>/dev/null || true
    elif command -v ufw &> /dev/null; then
        echo "配置ufw..."
        ufw --force enable
        ufw allow 80
        ufw allow 443
    fi
    
    echo "防火墙配置完成"
EOF

log_success "防火墙配置完成"

# 验证部署
log_info "7. 验证部署..."
ssh "$USER@$SERVER" << EOF
    echo "检查文件:"
    ls -la $REMOTE_DIR/index.html && echo "✅ index.html 存在" || echo "❌ index.html 不存在"
    ls -la $REMOTE_DIR/admin/ && echo "✅ admin目录 存在" || echo "❌ admin目录 不存在"
    ls -la $REMOTE_DIR/js/ && echo "✅ js目录 存在" || echo "❌ js目录 不存在"
    ls -la $REMOTE_DIR/css/ && echo "✅ css目录 存在" || echo "❌ css目录 不存在"
    
    echo ""
    echo "检查服务:"
    systemctl is-active nginx && echo "✅ Nginx 运行中" || echo "❌ Nginx 未运行"
    ss -tlnp | grep :80 && echo "✅ 端口80 监听中" || echo "❌ 端口80 未监听"
EOF

# 清理临时文件
rm -f /tmp/files_to_upload.txt

echo ""
echo "=========================================="
log_success "🎉 部署完成！"
echo "=========================================="
echo ""
echo "访问地址："
echo "  主页: http://$SERVER"
echo "  产品页面: http://$SERVER/products.html"
echo "  管理后台: http://$SERVER/admin/login.html"
echo "  用户登录: http://$SERVER/login.html"
echo ""
echo "管理员登录信息："
echo "  用户名: admin"
echo "  密码: admin123"
echo ""
echo "如果无法访问，请检查："
echo "1. 服务器防火墙是否开放80端口"
echo "2. 云服务商安全组是否开放80端口"
echo "3. 运行: ssh root@$SERVER 'systemctl status nginx'"
echo ""
