<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 安徽春晟机械有限公司</title>
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        .login-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            background: #be131b;
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .login-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        
        .login-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
        }
        
        .login-form {
            padding: 40px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #be131b;
        }
        
        .login-btn {
            width: 100%;
            padding: 12px;
            background: #be131b;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .login-btn:hover {
            background: #a01018;
        }
        
        .login-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .error-message {
            background: #fee;
            color: #c33;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            display: none;
            border: 1px solid #fcc;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            display: none;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #be131b;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>管理员登录</h1>
            <p>安徽春晟机械有限公司</p>
        </div>
        
        <div class="login-form">
            <div id="error-message" class="error-message"></div>
            
            <form id="login-form">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" required autocomplete="username" value="admin">
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" required autocomplete="current-password">
                </div>

                <!-- 验证码 -->
                <div class="form-group">
                    <label for="captcha-input">验证码</label>
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <input type="text" id="captcha-input" name="captcha" required
                               style="flex: 1;" placeholder="请输入验证码" maxlength="4">
                        <canvas id="captcha-canvas" style="border: 1px solid #e1e5e9; border-radius: 4px; cursor: pointer;" title="点击刷新验证码"></canvas>
                        <button type="button" id="refresh-captcha"
                                style="padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                            刷新
                        </button>
                    </div>
                    <small style="color: #666; font-size: 11px; margin-top: 5px; display: block;">点击验证码图片或刷新按钮可更换验证码</small>
                </div>

                <button type="submit" id="login-btn" class="login-btn">登录</button>
            </form>
            
            <div id="loading" class="loading">
                <div class="spinner"></div>
                正在登录...
            </div>
        </div>
    </div>

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <!-- 验证码组件 -->
    <script src="../js/captcha.js"></script>
    <script>
        // 简单的管理员登录系统
        const supabase = window.supabase.createClient(
            'https://snckktsqwrbfwtjlvcfr.supabase.co',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644'
        );

        // 初始化验证码
        let captcha;
        document.addEventListener('DOMContentLoaded', function() {
            captcha = window.CaptchaManager.create('captcha-canvas', {
                width: 100,
                height: 35,
                length: 4
            });

            // 刷新按钮事件
            document.getElementById('refresh-captcha').addEventListener('click', function() {
                captcha.refresh();
                document.getElementById('captcha-input').value = '';
            });
        });

        // 管理员登录功能
        document.getElementById('login-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const captchaInput = document.getElementById('captcha-input').value;
            const errorDiv = document.getElementById('error-message');
            const loadingDiv = document.getElementById('loading');
            const loginBtn = document.getElementById('login-btn');

            // 清除之前的错误信息
            errorDiv.style.display = 'none';

            // 验证验证码
            if (!captchaInput) {
                errorDiv.textContent = '请输入验证码';
                errorDiv.style.display = 'block';
                return;
            }

            if (!captcha.validate(captchaInput)) {
                errorDiv.textContent = '验证码错误，请重新输入';
                errorDiv.style.display = 'block';
                captcha.refresh(); // 刷新验证码
                document.getElementById('captcha-input').value = '';
                return;
            }

            // 显示加载状态
            loadingDiv.style.display = 'block';
            loginBtn.disabled = true;

            try {
                // 管理员验证 - 支持硬编码和数据库两种方式
                let isValidAdmin = false;
                let adminUser = null;

                // 方式1：硬编码验证（保持向后兼容）
                if (username === 'admin' && password === 'admin123') {
                    console.log('✅ 硬编码管理员凭据验证成功');
                    isValidAdmin = true;

                    // 尝试从数据库获取管理员信息
                    try {
                        const { data: userData, error } = await supabase
                            .from('users')
                            .select('*')
                            .eq('username', 'admin')
                            .eq('user_type', 'admin')
                            .limit(1);

                        if (!error && userData && userData.length > 0) {
                            adminUser = userData[0];
                            console.log('✅ 找到数据库中的管理员账户:', adminUser.username);
                        }
                    } catch (dbError) {
                        console.warn('⚠️ 无法从数据库获取管理员信息，使用硬编码模式');
                    }
                }

                // 方式2：数据库验证（查询用户表中的管理员）
                if (!isValidAdmin) {
                    try {
                        // 直接查询用户表中的管理员账户
                        const { data: userData, error: userError } = await supabase
                            .from('users')
                            .select('*')
                            .eq('user_type', 'admin')
                            .eq('is_active', true);

                        if (!userError && userData && userData.length > 0) {
                            // 查找匹配的管理员
                            const matchedAdmin = userData.find(user => {
                                // 支持用户名或邮箱登录
                                const usernameMatch = user.username === username;
                                const emailMatch = user.email === username;
                                
                                // 如果有密码哈希，验证密码
                                if (user.password_hash) {
                                    const storedPassword = atob(user.password_hash); // 解码密码
                                    return (usernameMatch || emailMatch) && storedPassword === password;
                                } else {
                                    // 如果没有密码哈希，只验证用户名/邮箱
                                    console.warn('⚠️ 用户没有设置密码，仅验证用户名');
                                    return (usernameMatch || emailMatch);
                                }
                            });

                            if (matchedAdmin) {
                                adminUser = matchedAdmin;
                                isValidAdmin = true;
                                console.log('✅ 数据库管理员认证成功:', adminUser.username);
                            }
                        }
                    } catch (dbAuthError) {
                        console.log('数据库认证失败:', dbAuthError.message);
                    }
                }

                if (isValidAdmin) {
                    // 设置会话标记
                    localStorage.setItem('admin_session', 'true');
                    localStorage.setItem('admin_login_time', new Date().toISOString());

                    // 如果有管理员用户信息，保存到localStorage
                    if (adminUser) {
                        localStorage.setItem('admin_user_info', JSON.stringify(adminUser));
                    }

                    // 跳转到产品管理页面
                    window.location.href = 'products.html';
                } else {
                    throw new Error('用户名或密码错误');
                }
                
            } catch (error) {
                console.error('登录失败:', error);
                errorDiv.textContent = error.message || '登录失败，请检查用户名和密码';
                errorDiv.style.display = 'block';
                // 登录失败时刷新验证码
                captcha.refresh();
                document.getElementById('captcha-input').value = '';
            } finally {
                loadingDiv.style.display = 'none';
                loginBtn.disabled = false;
            }
        });
        
        // 检查是否已经登录
        const adminSession = localStorage.getItem('admin_session');
        const loginTime = localStorage.getItem('admin_login_time');
        
        if (adminSession === 'true' && loginTime) {
            const loginDate = new Date(loginTime);
            const now = new Date();
            const hoursDiff = (now - loginDate) / (1000 * 60 * 60);
            
            if (hoursDiff < 24) {
                // 会话有效，直接跳转到产品管理页面
                window.location.href = 'products.html';
            }
        }
        
        // 回车键登录
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('login-form').dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>
