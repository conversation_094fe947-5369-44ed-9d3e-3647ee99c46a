// 管理员认证管理器
window.AdminAuth = class AdminAuth {
    constructor() {
        this.currentAdmin = null;
        this.isAuthenticated = false;
        this.init();
    }

    async init() {
        console.log('🔧 [ADMIN-AUTH] 初始化管理员认证系统');
        
        // 检查当前认证状态
        await this.checkAuthStatus();
        
        // 如果不在登录页面且未认证，重定向到登录页面
        if (!this.isAuthenticated && !window.location.pathname.includes('/admin/login.html')) {
            this.redirectToLogin();
        }
    }

    async checkAuthStatus() {
        try {
            console.log('🔍 [ADMIN-AUTH] 检查管理员认证状态');

            // 检查简单会话标记
            const adminSession = localStorage.getItem('admin_session');
            const loginTime = localStorage.getItem('admin_login_time');

            if (adminSession === 'true' && loginTime) {
                // 检查会话是否过期（24小时）
                const loginDate = new Date(loginTime);
                const now = new Date();
                const hoursDiff = (now - loginDate) / (1000 * 60 * 60);

                if (hoursDiff < 24) {
                    console.log('🔍 [ADMIN-AUTH] 找到有效的管理员会话');

                    // 获取管理员信息
                    const { data: userData, error } = await supabase
                        .from('users')
                        .select('*')
                        .eq('username', 'admin')
                        .eq('user_type', 'admin')
                        .limit(1);

                    if (error) {
                        console.error('🚫 [ADMIN-AUTH] 查询管理员数据失败:', error);
                        throw error;
                    }

                    if (userData && userData.length > 0) {
                        const adminUser = userData[0];
                        console.log('✅ [ADMIN-AUTH] 管理员认证成功:', adminUser.username);
                        this.currentAdmin = adminUser;
                        this.isAuthenticated = true;
                        this.updateAdminUI();
                    } else {
                        console.log('🚫 [ADMIN-AUTH] 管理员账户不存在');
                        this.logout();
                    }
                } else {
                    console.log('🚫 [ADMIN-AUTH] 管理员会话已过期');
                    this.logout();
                }
            } else {
                console.log('🚫 [ADMIN-AUTH] 没有管理员会话');
                this.isAuthenticated = false;
                this.currentAdmin = null;
            }
        } catch (error) {
            console.error('🚫 [ADMIN-AUTH] 认证检查失败:', error);
            this.isAuthenticated = false;
            this.currentAdmin = null;
        }
    }

    async login(username, password) {
        try {
            console.log('🔐 [ADMIN-AUTH] 尝试管理员登录:', username);

            // 简单的管理员验证
            if (username === 'admin' && password === 'admin123') {
                console.log('✅ [ADMIN-AUTH] 管理员凭据验证成功');

                // 获取管理员用户信息
                const { data: userData, error: userError } = await supabase
                    .from('users')
                    .select('*')
                    .eq('username', 'admin')
                    .eq('user_type', 'admin')
                    .limit(1);

                if (userError || !userData || userData.length === 0) {
                    console.error('🚫 [ADMIN-AUTH] 管理员账户不存在或未激活');
                    throw new Error('管理员账户不存在');
                }

                const adminUser = userData[0];
                console.log('✅ [ADMIN-AUTH] 管理员登录成功:', adminUser.username);
                this.currentAdmin = adminUser;
                this.isAuthenticated = true;

                // 设置会话标记
                localStorage.setItem('admin_session', 'true');
                localStorage.setItem('admin_login_time', new Date().toISOString());

                // 重定向到管理后台
                window.location.href = '/admin/dashboard.html';

                return { success: true };
            } else {
                throw new Error('用户名或密码错误');
            }

        } catch (error) {
            console.error('🚫 [ADMIN-AUTH] 管理员登录失败:', error);
            return { success: false, error: error.message };
        }
    }

    async logout() {
        try {
            console.log('🚪 [ADMIN-AUTH] 管理员退出登录');

            // 清除会话标记
            localStorage.removeItem('admin_session');
            localStorage.removeItem('admin_login_time');

            this.currentAdmin = null;
            this.isAuthenticated = false;

            // 重定向到登录页面
            this.redirectToLogin();

        } catch (error) {
            console.error('🚫 [ADMIN-AUTH] 退出登录失败:', error);
        }
    }

    redirectToLogin() {
        if (!window.location.pathname.includes('/admin/login.html')) {
            console.log('🔄 [ADMIN-AUTH] 重定向到管理员登录页面');
            window.location.href = '/admin/login.html';
        }
    }

    updateAdminUI() {
        // 更新管理后台UI，显示管理员信息
        const adminNameElements = document.querySelectorAll('.admin-name');
        const adminEmailElements = document.querySelectorAll('.admin-email');
        
        adminNameElements.forEach(el => {
            el.textContent = this.currentAdmin.username || this.currentAdmin.email;
        });
        
        adminEmailElements.forEach(el => {
            el.textContent = this.currentAdmin.email;
        });
    }

    // 检查是否为管理员
    isAdmin() {
        return this.isAuthenticated && this.currentAdmin && this.currentAdmin.user_type === 'admin';
    }

    // 获取当前管理员信息
    getCurrentAdmin() {
        return this.currentAdmin;
    }

    // 权限检查中间件
    requireAdmin() {
        if (!this.isAdmin()) {
            this.redirectToLogin();
            return false;
        }
        return true;
    }
}

// 创建全局管理员认证实例
let adminAuth;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 确保Supabase已加载
    if (typeof window.supabase !== 'undefined' && !window.adminAuth) {
        window.adminAuth = new window.AdminAuth();
    } else if (typeof window.supabase === 'undefined') {
        console.error('🚫 [ADMIN-AUTH] Supabase未加载，无法初始化管理员认证');
    }
});

// 如果页面已经加载完成，立即初始化
if (document.readyState === 'loading') {
    // 文档仍在加载中，等待DOMContentLoaded事件
} else {
    // 文档已经加载完成，立即初始化
    setTimeout(() => {
        if (typeof window.supabase !== 'undefined' && !window.adminAuth) {
            window.adminAuth = new window.AdminAuth();
        }
    }, 100);
}
