@font-face {
  font-family: "iconfont"; /* Project id 1997181 */
  src: url('iconfont.woff2?t=1747296419609') format('woff2'),
       url('iconfont.woff?t=1747296419609') format('woff'),
       url('iconfont.ttf?t=1747296419609') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.iconhelp21:before {
  content: "\e8c4";
}

.iconclose2:before {
  content: "\e8bf";
}

.iconstyle:before {
  content: "\e8be";
}

.iconInteraction:before {
  content: "\e8bd";
}

.icondelete1:before {
  content: "\e8bb";
}

.iconhelp2:before {
  content: "\e8ba";
}

.iconarray:before {
  content: "\e8b9";
}

.iconAnimation:before {
  content: "\e8b8";
}

.icondata:before {
  content: "\e8b7";
}

.icona-addto:before {
  content: "\e8a6";
}

.iconfinish:before {
  content: "\e8a5";
}

.iconwarning2:before {
  content: "\e8a4";
}

.icona-unknownfile:before {
  content: "\e8a3";
}

.iconrenew:before {
  content: "\e8a2";
}

.icondownload:before {
  content: "\e8a1";
}

.icona-notadd:before {
  content: "\e89d";
}

.iconadded:before {
  content: "\e899";
}

.iconmaterial:before {
  content: "\e87c";
}

.iconhide:before {
  content: "\e865";
}

.icona-invokeapi:before {
  content: "\e863";
}

.iconhistory:before {
  content: "\e860";
}

.icona-workorder2:before {
  content: "\e84a";
}

.iconintelligence:before {
  content: "\e833";
}

.iconfail:before {
  content: "\e832";
}

.iconadd:before {
  content: "\e831";
}

.icona-businesscard:before {
  content: "\e830";
}

.icona-turnoff:before {
  content: "\e82f";
}

.iconopen:before {
  content: "\e82e";
}

.iconunchecked:before {
  content: "\e82d";
}

.iconrefresh1:before {
  content: "\e82c";
}

.iconwechat:before {
  content: "\e82b";
}

.icona-successcolor:before {
  content: "\e82a";
}

.iconsuccess:before {
  content: "\e829";
}

.iconwarning:before {
  content: "\e828";
}

.icona-customerservice:before {
  content: "\e827";
}

.icona-closedown:before {
  content: "\e826";
}

.iconright:before {
  content: "\e824";
}

.iconexclusive:before {
  content: "\e821";
}

.icona-rightarrow3:before {
  content: "\e80b";
}

.icona-leftarrow3:before {
  content: "\e80a";
}

.iconnavigation:before {
  content: "\e809";
}

.icona-downarrow:before {
  content: "\e808";
}

.icona-uparrow:before {
  content: "\e807";
}

.iconclose:before {
  content: "\e806";
}

.icona-rightarrow2:before {
  content: "\e7ee";
}

.icona-leftarrow2:before {
  content: "\e7ed";
}

.icona-rightarrow1:before {
  content: "\e7ec";
}

.icona-leftarrow1:before {
  content: "\e7eb";
}

.icona-rightarrow:before {
  content: "\e7ea";
}

.icona-leftarrow:before {
  content: "\e7e9";
}

.iconweixin:before {
  content: "\e766";
}

.iconcopy:before {
  content: "\e765";
}

.iconpreview:before {
  content: "\e764";
}

.iconsave:before {
  content: "\e763";
}

.icontranslatehover:before {
  content: "\e728";
}

.icontranslate:before {
  content: "\e727";
}

.iconmaskpicture:before {
  content: "\e723";
}

.iconicon-move:before {
  content: "\e722";
}

.iconbeta:before {
  content: "\e71f";
}

.iconfileupload:before {
  content: "\e71e";
}

.iconforward:before {
  content: "\e71c";
}

.iconreturn:before {
  content: "\e71d";
}

.iconuploadfailed:before {
  content: "\e71a";
}

.iconpdf:before {
  content: "\e719";
}

.iconword:before {
  content: "\e718";
}

.iconppt:before {
  content: "\e717";
}

.iconretry:before {
  content: "\e6fe";
}

.icondelete:before {
  content: "\e6fd";
}

.iconpopup:before {
  content: "\e6fc";
}

.iconsystem:before {
  content: "\e6f7";
}

.iconform:before {
  content: "\e6f6";
}

.icondevelop:before {
  content: "\e6f3";
}

.icondesign:before {
  content: "\e6f2";
}

.iconfunction:before {
  content: "\e6f1";
}

.iconcontent:before {
  content: "\e6f0";
}

.iconpicture:before {
  content: "\e6ef";
}

.iconswitchstyles:before {
  content: "\e6ee";
}

.iconrefresh:before {
  content: "\e6e7";
}

.iconscan:before {
  content: "\e6e6";
}

.iconcheckmark:before {
  content: "\e6e5";
}

.iconicon-colorfail:before {
  content: "\e6e4";
}

.iconupload:before {
  content: "\e6e1";
}

.icontips:before {
  content: "\e6e0";
}

.iconhelp:before {
  content: "\e6df";
}

.iconicon-des-spot:before {
  content: "\e617";
}

.iconicon-des-fillet:before {
  content: "\e616";
}

.iconicon-des-Mask:before {
  content: "\e615";
}

