#!/bin/bash

# 最简单的部署脚本 - 使用Python内置HTTP服务器

echo "=== 春盛网站最简单部署脚本 ==="

# 1. 创建网站目录
echo "创建网站目录..."
mkdir -p /var/www/chunsheng-website
cd /var/www/chunsheng-website

# 2. 创建一个简单的测试页面
echo "创建测试页面..."
cat > index.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>春盛机械 - 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .status {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 春盛机械网站部署成功！</h1>
        
        <div class="status">
            ✅ 服务器连接正常<br>
            ✅ Python HTTP服务器运行中<br>
            ✅ 网站可以正常访问
        </div>
        
        <div class="info">
            <strong>服务器信息：</strong><br>
            • 公网IP: **************<br>
            • 私有IP: *************<br>
            • 服务类型: Python内置HTTP服务器<br>
            • 部署目录: /var/www/chunsheng-website
        </div>
        
        <h2>下一步操作：</h2>
        <ol>
            <li>上传完整的网站文件</li>
            <li>配置域名解析（如果需要）</li>
            <li>设置防火墙规则</li>
            <li>配置SSL证书（如果需要HTTPS）</li>
        </ol>
        
        <p style="text-align: center; margin-top: 30px;">
            <strong>春盛机械有限公司</strong><br>
            网站部署时间: <span id="datetime"></span>
        </p>
    </div>
    
    <script>
        document.getElementById('datetime').textContent = new Date().toLocaleString('zh-CN');
    </script>
</body>
</html>
EOF

# 3. 启动Python HTTP服务器
echo "启动Python HTTP服务器..."
echo "网站将在以下地址访问："
echo "http://**************:8000"
echo "http://*************:8000"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

# 使用Python 2.7启动HTTP服务器
python -m SimpleHTTPServer 8000
