<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置tex用户密码</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin: 10px 0;
        }
        button:hover {
            background-color: #0056b3;
        }
        .message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .user-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 设置tex用户密码</h1>
        
        <div class="user-info">
            <h3>操作说明：</h3>
            <p>将为tex用户（<EMAIL>）设置密码：<strong>123456</strong></p>
            <p>使用简化认证系统，不依赖Supabase Auth</p>
        </div>
        
        <button onclick="setPassword()">设置tex用户密码为123456</button>
        <button onclick="testLogin()" style="background-color: #28a745;">测试登录</button>
        
        <div id="message" class="message"></div>
    </div>

    <!-- 引入 Supabase -->
    <script src="supabase-js.min.js"></script>
    <script src="js/supabase-config.js"></script>
    <script src="js/simple-auth.js"></script>
    
    <script>
        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = text;
            messageDiv.style.display = 'block';
        }

        async function setPassword() {
            try {
                showMessage('正在设置tex用户密码...', 'info');

                // 使用简化认证系统的哈希方法
                const password = '123456';
                const hashedPassword = btoa(password + 'chunsheng_salt');

                console.log('计算的密码哈希:', hashedPassword);

                // 更新数据库中的密码哈希
                const { error } = await supabase
                    .from('users')
                    .update({ password_hash: hashedPassword })
                    .eq('email', '<EMAIL>');

                if (error) {
                    throw error;
                }

                showMessage(`✅ tex用户密码设置成功！<br>
                    邮箱: <EMAIL><br>
                    密码: 123456<br>
                    哈希值: ${hashedPassword}`, 'success');

            } catch (error) {
                console.error('设置密码失败:', error);
                showMessage('❌ 设置密码失败: ' + error.message, 'error');
            }
        }

        async function testLogin() {
            try {
                showMessage('正在测试登录...', 'info');

                // 使用简化认证系统登录
                const result = await window.simpleAuth.loginUser('<EMAIL>', '123456');

                if (result.success) {
                    showMessage(`✅ 登录测试成功！<br>
                        用户: ${result.user.username}<br>
                        邮箱: ${result.user.email}<br>
                        权限: ${result.user.user_type}<br>
                        公司: ${result.user.company_name || '未填写'}`, 'success');

                    // 3秒后自动登出
                    setTimeout(() => {
                        window.simpleAuth.logout();
                        showMessage('✅ 测试完成，已自动登出', 'info');
                    }, 3000);
                } else {
                    showMessage('❌ 登录测试失败: ' + result.message, 'error');
                }

            } catch (error) {
                console.error('登录测试失败:', error);
                showMessage('❌ 登录测试失败: ' + error.message, 'error');
            }
        }

        // 页面加载时显示当前状态
        window.addEventListener('load', function() {
            setTimeout(() => {
                showMessage('✅ 系统已就绪，可以设置密码', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
