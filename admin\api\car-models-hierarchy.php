<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 数据库配置
$host = 'aws-0-ap-southeast-1.pooler.supabase.com';
$port = '6543';
$dbname = 'postgres';
$username = 'postgres.snckktsqwrbfwtjlvcfr';
$password = 'Mm@124578';

try {
    $dsn = "pgsql:host=$host;port=$port;dbname=$dbname";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);

    $action = $_GET['action'] ?? '';

    switch ($action) {
        case 'brands':
            // 获取所有品牌列表
            $stmt = $pdo->prepare("SELECT DISTINCT brand FROM car_models ORDER BY brand");
            $stmt->execute();
            $brands = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            echo json_encode([
                'success' => true,
                'data' => $brands
            ]);
            break;

        case 'models':
            // 获取指定品牌下的车型列表
            $brand = $_GET['brand'] ?? '';
            if (empty($brand)) {
                echo json_encode([
                    'success' => false,
                    'message' => '品牌参数不能为空'
                ]);
                break;
            }

            $stmt = $pdo->prepare("SELECT id, model FROM car_models WHERE brand = ? ORDER BY model");
            $stmt->execute([$brand]);
            $models = $stmt->fetchAll();
            
            echo json_encode([
                'success' => true,
                'data' => $models
            ]);
            break;

        case 'hierarchy':
            // 获取完整的品牌车型层级结构
            $stmt = $pdo->prepare("SELECT brand, model, id FROM car_models ORDER BY brand, model");
            $stmt->execute();
            $carModels = $stmt->fetchAll();

            $hierarchy = [];
            foreach ($carModels as $carModel) {
                $brand = $carModel['brand'];
                if (!isset($hierarchy[$brand])) {
                    $hierarchy[$brand] = [];
                }
                $hierarchy[$brand][] = [
                    'id' => $carModel['id'],
                    'model' => $carModel['model']
                ];
            }

            echo json_encode([
                'success' => true,
                'data' => $hierarchy
            ]);
            break;

        case 'products-by-car-model':
            // 根据车型查询产品
            $brand = $_GET['brand'] ?? '';
            $model = $_GET['model'] ?? '';
            
            if (empty($brand) && empty($model)) {
                echo json_encode([
                    'success' => false,
                    'message' => '至少需要提供品牌或车型参数'
                ]);
                break;
            }

            $sql = "SELECT DISTINCT p.* FROM products p 
                    INNER JOIN product_car_models pcm ON p.id = pcm.product_id 
                    INNER JOIN car_models cm ON pcm.car_model_id = cm.id 
                    WHERE 1=1";
            $params = [];

            if (!empty($brand)) {
                $sql .= " AND cm.brand = ?";
                $params[] = $brand;
            }

            if (!empty($model)) {
                $sql .= " AND cm.model = ?";
                $params[] = $model;
            }

            $sql .= " ORDER BY p.id";

            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            $products = $stmt->fetchAll();

            echo json_encode([
                'success' => true,
                'data' => $products
            ]);
            break;

        case 'product-car-models':
            // 获取产品关联的车型
            $productId = $_GET['product_id'] ?? '';
            if (empty($productId)) {
                echo json_encode([
                    'success' => false,
                    'message' => '产品ID不能为空'
                ]);
                break;
            }

            $stmt = $pdo->prepare("
                SELECT cm.id, cm.brand, cm.model 
                FROM car_models cm 
                INNER JOIN product_car_models pcm ON cm.id = pcm.car_model_id 
                WHERE pcm.product_id = ? 
                ORDER BY cm.brand, cm.model
            ");
            $stmt->execute([$productId]);
            $carModels = $stmt->fetchAll();

            echo json_encode([
                'success' => true,
                'data' => $carModels
            ]);
            break;

        default:
            echo json_encode([
                'success' => false,
                'message' => '无效的操作'
            ]);
            break;
    }

} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => '数据库错误: ' . $e->getMessage()
    ]);
}
?>
