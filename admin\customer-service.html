<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服管理 - 安徽春晟机械有限公司</title>
    
    <!-- 样式文件 -->
    <link href="../css/custom.css" rel="stylesheet" type="text/css">
    
    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <style>
        /* 导航栏美化样式 */
        .admin-nav {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-brand {
            padding: 20px 30px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .nav-brand h2 {
            margin: 0;
            color: white;
            font-size: 24px;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .nav-menu {
            display: flex;
            padding: 0 20px 20px 20px;
            gap: 15px;
            flex-wrap: wrap;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 20px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-weight: 500;
            min-width: 120px;
            justify-content: center;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .nav-btn.active {
            background: rgba(255, 255, 255, 0.25);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .nav-btn.logout-btn {
            background: rgba(220, 53, 69, 0.8);
            margin-left: auto;
        }

        .nav-btn.logout-btn:hover {
            background: rgba(220, 53, 69, 1);
        }

        .nav-btn .icon {
            font-size: 18px;
        }

        .admin-container {
            min-height: 100vh;
            background: #f8f9fa;
        }

        .admin-main {
            padding: 30px;
        }

        .cs-container {
            display: flex;
            height: calc(100vh - 120px);
            gap: 20px;
        }
        
        .cs-sidebar {
            width: 300px;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            overflow-y: auto;
        }
        
        .cs-main {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }
        
        .session-item {
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .session-item:hover {
            background: #f0f0f0;
        }
        
        .session-item.active {
            background: #e3f2fd;
            border-color: #2196f3;
        }
        
        .session-status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: white;
        }
        
        .status-waiting { background: #ff9800; }
        .status-active { background: #4caf50; }
        .status-closed { background: #9e9e9e; }
        
        .chat-area {
            flex: 1;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            margin-bottom: 20px;
        }
        
        .chat-header {
            padding: 15px;
            background: #f5f5f5;
            border-bottom: 1px solid #e0e0e0;
            border-radius: 8px 8px 0 0;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            max-height: 400px;
        }
        
        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message.agent,
        .message.admin {
            justify-content: flex-start;
        }

        .message.system {
            justify-content: center;
        }
        
        .message-content {
            max-width: 70%;
            padding: 10px 15px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .message.user .message-content {
            background: #2196f3;
            color: white;
        }
        
        .message.agent .message-content,
        .message.admin .message-content {
            background: #f0f0f0;
            color: #333;
        }
        
        .message.system .message-content {
            background: #fff3cd;
            color: #856404;
            font-style: italic;
        }
        
        .message-time {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }
        
        .chat-input {
            padding: 15px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            gap: 10px;
        }
        
        .chat-input input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 20px;
            outline: none;
        }
        
        .chat-input button {
            padding: 10px 20px;
            background: #2196f3;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
        }
        

        
        .agent-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .status-online { background: #4caf50; }
        .status-offline { background: #9e9e9e; }
        .status-busy { background: #ff9800; }
        
        .no-session {
            text-align: center;
            color: #999;
            padding: 50px;
        }

        .search-box {
            margin-bottom: 20px;
        }

        .search-box input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            outline: none;
        }

        .user-item {
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }

        .user-item:hover {
            background: #f0f0f0;
        }

        .user-item.active {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .user-item.has-unread::after {
            content: '';
            position: absolute;
            top: 10px;
            right: 10px;
            width: 8px;
            height: 8px;
            background: #f44336;
            border-radius: 50%;
        }

        .user-info h4 {
            margin: 0 0 5px 0;
            font-size: 14px;
        }

        .user-info p {
            margin: 0;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 导航栏 -->
        <nav class="admin-nav">
            <div class="nav-brand">
                <h2>春晟机械管理后台</h2>
            </div>
            <div class="nav-menu">
                <a href="products.html" class="nav-btn">
                    <i class="icon">📦</i>
                    <span>产品管理</span>
                </a>
                <a href="users.html" class="nav-btn">
                    <i class="icon">👥</i>
                    <span>用户管理</span>
                </a>
                <a href="car-models.html" class="nav-btn">
                    <i class="icon">🚗</i>
                    <span>车型管理</span>
                </a>
                <a href="customer-service.html" class="nav-btn active">
                    <i class="icon">💬</i>
                    <span>客服管理</span>
                </a>
                <a href="#" onclick="logout()" class="nav-btn logout-btn">
                    <i class="icon">🚪</i>
                    <span>退出</span>
                </a>
            </div>
        </nav>

        <!-- 主要内容 -->
        <main class="admin-main">
            <h1 style="color: #333; margin-bottom: 30px; font-size: 28px;">客服消息管理</h1>

            <!-- 客服消息管理 -->
            <div class="cs-container">
                <div class="cs-sidebar">
                    <h3>用户列表</h3>
                    <div class="search-box">
                        <input type="text" id="user-search" placeholder="搜索用户..." onkeyup="searchUsers()">
                    </div>
                    <div id="users-list">
                        <!-- 用户列表将在这里动态加载 -->
                    </div>
                </div>

                <div class="cs-main">
                    <div id="chat-container" class="chat-area">
                        <div class="no-session">
                            请选择一个用户查看对话
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 脚本文件 -->
    <script src="../js/supabase-config.js"></script>
    <script src="js/admin-common.js"></script>
    <script src="js/customer-service-admin.js"></script>
</body>
</html>
