[build]
  publish = "."
  command = "echo 'Static site - no build needed'"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/index.php"
  to = "/"
  status = 301

[[redirects]]
  from = "/products.php"
  to = "/products"
  status = 301

[[redirects]]
  from = "/about.php"
  to = "/about"
  status = 301

[[redirects]]
  from = "/contact.php"
  to = "/contact"
  status = 301

[[redirects]]
  from = "/factory.php"
  to = "/factory"
  status = 301

# SPA fallback for admin routes
[[redirects]]
  from = "/admin/*"
  to = "/admin/:splat"
  status = 200

# Clean URLs
[[redirects]]
  from = "/products"
  to = "/products.html"
  status = 200

[[redirects]]
  from = "/about"
  to = "/about.html"
  status = 200

[[redirects]]
  from = "/contact"
  to = "/contact.html"
  status = 200

[[redirects]]
  from = "/factory"
  to = "/factory.html"
  status = 200

[[redirects]]
  from = "/login"
  to = "/login.html"
  status = 200

[[redirects]]
  from = "/register"
  to = "/register.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self' https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https://snckktsqwrbfwtjlvcfr.supabase.co https://*.supabase.co"

[[headers]]
  for = "/admin/*"
  [headers.values]
    X-Robots-Tag = "noindex, nofollow"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.jpg"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.png"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.pdf"
  [headers.values]
    Cache-Control = "public, max-age=86400"
    Content-Disposition = "inline"
