#!/bin/bash

# 宝塔面板自动安装脚本
# 服务器: **************

echo "=========================================="
echo "  在服务器上安装宝塔面板"
echo "  服务器: **************"
echo "=========================================="
echo ""

SERVER="**************"
USER="root"

echo "正在连接服务器并安装宝塔面板..."
echo "密码: Mm.124578"
echo ""

# 创建安装脚本
cat > baota_install.sh << 'EOF'
#!/bin/bash

echo "开始安装宝塔面板..."

# 检测系统类型
if [ -f /etc/redhat-release ]; then
    echo "检测到CentOS/RHEL系统"
    # CentOS安装命令
    yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh ed8484bec
elif [ -f /etc/debian_version ]; then
    echo "检测到Ubuntu/Debian系统"
    # Ubuntu安装命令
    wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh ed8484bec
else
    echo "尝试通用安装方式..."
    wget -O install.sh http://download.bt.cn/install/install_6.0.sh && bash install.sh ed8484bec
fi

echo "宝塔面板安装完成"
echo "请记录以下信息："
echo "面板地址: http://服务器IP:8888"
echo "用户名和密码将在安装完成后显示"
EOF

chmod +x baota_install.sh

echo "安装脚本已创建，现在连接服务器执行安装..."
echo "请输入服务器密码: Mm.124578"
echo ""

# 上传并执行安装脚本
scp baota_install.sh root@$SERVER:/tmp/
ssh root@$SERVER "cd /tmp && chmod +x baota_install.sh && ./baota_install.sh"

echo ""
echo "=========================================="
echo "宝塔面板安装完成！"
echo "=========================================="
echo ""
echo "访问信息："
echo "面板地址: http://**************:8888"
echo "用户名和密码请查看上面的安装输出"
echo ""
echo "下一步："
echo "1. 在浏览器中访问宝塔面板"
echo "2. 使用显示的用户名和密码登录"
echo "3. 安装LNMP环境（Linux + Nginx + MySQL + PHP）"
echo "4. 创建网站并上传文件"
echo ""

# 清理临时文件
rm -f baota_install.sh
