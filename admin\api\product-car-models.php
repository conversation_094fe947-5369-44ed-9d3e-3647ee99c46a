<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 数据库配置
$host = 'aws-0-ap-southeast-1.pooler.supabase.com';
$port = '6543';
$dbname = 'postgres';
$username = 'postgres.snckktsqwrbfwtjlvcfr';
$password = 'Mm@124578';

try {
    $dsn = "pgsql:host=$host;port=$port;dbname=$dbname";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);

    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'GET':
            // 获取产品关联的车型
            $productId = $_GET['product_id'] ?? '';
            if (empty($productId)) {
                echo json_encode([
                    'success' => false,
                    'message' => '产品ID不能为空'
                ]);
                break;
            }

            $stmt = $pdo->prepare("
                SELECT cm.id, cm.brand, cm.model 
                FROM car_models cm 
                INNER JOIN product_car_models pcm ON cm.id = pcm.car_model_id 
                WHERE pcm.product_id = ? 
                ORDER BY cm.brand, cm.model
            ");
            $stmt->execute([$productId]);
            $carModels = $stmt->fetchAll();

            echo json_encode([
                'success' => true,
                'data' => $carModels
            ]);
            break;

        case 'POST':
            // 添加产品车型关联
            $input = json_decode(file_get_contents('php://input'), true);
            $productId = $input['product_id'] ?? '';
            $carModelIds = $input['car_model_ids'] ?? [];

            if (empty($productId) || empty($carModelIds)) {
                echo json_encode([
                    'success' => false,
                    'message' => '产品ID和车型ID不能为空'
                ]);
                break;
            }

            $pdo->beginTransaction();

            try {
                // 先删除现有关联
                $stmt = $pdo->prepare("DELETE FROM product_car_models WHERE product_id = ?");
                $stmt->execute([$productId]);

                // 添加新关联
                $stmt = $pdo->prepare("INSERT INTO product_car_models (product_id, car_model_id) VALUES (?, ?)");
                foreach ($carModelIds as $carModelId) {
                    $stmt->execute([$productId, $carModelId]);
                }

                // 更新products表的car_models字段（用于显示）
                $stmt = $pdo->prepare("
                    SELECT STRING_AGG(cm.brand || cm.model, ',') as car_models_str
                    FROM car_models cm 
                    INNER JOIN product_car_models pcm ON cm.id = pcm.car_model_id 
                    WHERE pcm.product_id = ?
                ");
                $stmt->execute([$productId]);
                $result = $stmt->fetch();
                $carModelsStr = $result['car_models_str'] ?? '';

                $stmt = $pdo->prepare("UPDATE products SET car_models = ? WHERE id = ?");
                $stmt->execute([$carModelsStr, $productId]);

                $pdo->commit();

                echo json_encode([
                    'success' => true,
                    'message' => '产品车型关联更新成功'
                ]);

            } catch (Exception $e) {
                $pdo->rollBack();
                throw $e;
            }
            break;

        case 'DELETE':
            // 删除产品车型关联
            $productId = $_GET['product_id'] ?? '';
            $carModelId = $_GET['car_model_id'] ?? '';

            if (empty($productId) || empty($carModelId)) {
                echo json_encode([
                    'success' => false,
                    'message' => '产品ID和车型ID不能为空'
                ]);
                break;
            }

            $stmt = $pdo->prepare("DELETE FROM product_car_models WHERE product_id = ? AND car_model_id = ?");
            $stmt->execute([$productId, $carModelId]);

            if ($stmt->rowCount() > 0) {
                // 更新products表的car_models字段
                $stmt = $pdo->prepare("
                    SELECT STRING_AGG(cm.brand || cm.model, ',') as car_models_str
                    FROM car_models cm 
                    INNER JOIN product_car_models pcm ON cm.id = pcm.car_model_id 
                    WHERE pcm.product_id = ?
                ");
                $stmt->execute([$productId]);
                $result = $stmt->fetch();
                $carModelsStr = $result['car_models_str'] ?? '';

                $stmt = $pdo->prepare("UPDATE products SET car_models = ? WHERE id = ?");
                $stmt->execute([$carModelsStr, $productId]);

                echo json_encode([
                    'success' => true,
                    'message' => '车型关联删除成功'
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => '未找到要删除的关联记录'
                ]);
            }
            break;

        default:
            echo json_encode([
                'success' => false,
                'message' => '不支持的请求方法'
            ]);
            break;
    }

} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => '数据库错误: ' . $e->getMessage()
    ]);
}
?>
