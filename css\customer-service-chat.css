/* 客服聊天窗口样式 */

.cs-chat-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.cs-chat-button {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: white;
    font-size: 24px;
}

.cs-chat-button:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.cs-chat-window {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 350px;
    height: 500px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    display: none;
    flex-direction: column;
    overflow: hidden;
    animation: slideUp 0.3s ease-out;
}

.cs-chat-window.show {
    display: flex;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.cs-chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.cs-chat-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.cs-chat-close {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cs-chat-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    opacity: 0.9;
}

.cs-status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4ade80;
}

.cs-chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8fafc;
}

.cs-message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-end;
}

.cs-message.user {
    justify-content: flex-end;
}

.cs-message.agent {
    justify-content: flex-start;
}

.cs-message.system {
    justify-content: center;
}

.cs-message-content {
    max-width: 80%;
    padding: 10px 14px;
    border-radius: 18px;
    word-wrap: break-word;
    font-size: 14px;
    line-height: 1.4;
}

.cs-message.user .cs-message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom-right-radius: 4px;
}

.cs-message.agent .cs-message-content {
    background: white;
    color: #374151;
    border: 1px solid #e5e7eb;
    border-bottom-left-radius: 4px;
}

.cs-message.system .cs-message-content {
    background: #fef3c7;
    color: #92400e;
    font-style: italic;
    text-align: center;
    border-radius: 12px;
}

.cs-message-time {
    font-size: 11px;
    color: #9ca3af;
    margin-top: 4px;
    text-align: center;
}

.cs-chat-input {
    padding: 15px 20px;
    background: white;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 10px;
    align-items: center;
}

.cs-chat-input input {
    flex: 1;
    padding: 10px 14px;
    border: 1px solid #d1d5db;
    border-radius: 20px;
    outline: none;
    font-size: 14px;
    transition: border-color 0.2s;
}

.cs-chat-input input:focus {
    border-color: #667eea;
}

.cs-chat-send {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s;
}

.cs-chat-send:hover {
    transform: scale(1.05);
}

.cs-chat-send:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.cs-typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 10px 14px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 18px;
    border-bottom-left-radius: 4px;
    max-width: 80%;
}

.cs-typing-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #9ca3af;
    animation: typing 1.4s infinite ease-in-out;
}

.cs-typing-dot:nth-child(1) { animation-delay: -0.32s; }
.cs-typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.cs-welcome-message {
    text-align: center;
    padding: 30px 20px;
    color: #6b7280;
}

.cs-welcome-message h5 {
    margin: 0 0 10px 0;
    color: #374151;
    font-size: 16px;
}

.cs-welcome-message p {
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
}

.cs-quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 15px;
}

.cs-quick-action {
    padding: 6px 12px;
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 16px;
    font-size: 12px;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s;
}

.cs-quick-action:hover {
    background: #e5e7eb;
    border-color: #9ca3af;
}

.cs-connection-status {
    padding: 8px 20px;
    background: #fef2f2;
    color: #dc2626;
    font-size: 12px;
    text-align: center;
    border-top: 1px solid #fecaca;
}

.cs-connection-status.connected {
    background: #f0fdf4;
    color: #16a34a;
    border-top-color: #bbf7d0;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .cs-chat-widget {
        bottom: 10px;
        right: 10px;
    }
    
    .cs-chat-window {
        width: calc(100vw - 20px);
        height: calc(100vh - 100px);
        bottom: 70px;
        right: -10px;
    }
    
    .cs-chat-button {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}

/* 滚动条样式 */
.cs-chat-messages::-webkit-scrollbar {
    width: 4px;
}

.cs-chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.cs-chat-messages::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 2px;
}

.cs-chat-messages::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}
