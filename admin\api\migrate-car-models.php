<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 数据库配置
$host = 'aws-0-ap-southeast-1.pooler.supabase.com';
$port = '6543';
$dbname = 'postgres';
$username = 'postgres.snckktsqwrbfwtjlvcfr';
$password = 'Mm@124578';

try {
    $dsn = "pgsql:host=$host;port=$port;dbname=$dbname";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);

    // 品牌映射表，用于准确识别品牌
    $brandMap = [
        '奥迪' => ['奥迪'],
        '宝马' => ['宝马'],
        '奔驰' => ['奔驰'],
        '大众' => ['大众'],
        '丰田' => ['丰田'],
        '本田' => ['本田'],
        '日产' => ['日产'],
        '现代' => ['现代'],
        '起亚' => ['起亚'],
        '福特' => ['福特'],
        '雪佛兰' => ['雪佛兰'],
        '别克' => ['别克'],
        '凯迪拉克' => ['凯迪拉克'],
        '沃尔沃' => ['沃尔沃'],
        '捷豹' => ['捷豹'],
        '路虎' => ['路虎'],
        '保时捷' => ['保时捷'],
        '法拉利' => ['法拉利'],
        '兰博基尼' => ['兰博基尼'],
        '玛莎拉蒂' => ['玛莎拉蒂']
    ];

    // 解析车型字符串，提取品牌和车型
    function parseCarModel($carModelStr, $brandMap) {
        $carModelStr = trim($carModelStr);
        if (empty($carModelStr)) {
            return null;
        }

        // 遍历品牌映射表，找到匹配的品牌
        foreach ($brandMap as $brand => $aliases) {
            foreach ($aliases as $alias) {
                if (strpos($carModelStr, $alias) === 0) {
                    $model = trim(substr($carModelStr, strlen($alias)));
                    if (!empty($model)) {
                        return ['brand' => $brand, 'model' => $model];
                    }
                }
            }
        }

        // 如果没有找到匹配的品牌，尝试其他方法
        // 假设前面的中文字符是品牌，后面的是车型
        if (preg_match('/^([\x{4e00}-\x{9fff}]+)(.+)$/u', $carModelStr, $matches)) {
            return ['brand' => trim($matches[1]), 'model' => trim($matches[2])];
        }

        return null;
    }

    $pdo->beginTransaction();

    $migratedCount = 0;
    $errorCount = 0;
    $errors = [];

    // 获取所有有车型数据的产品
    $stmt = $pdo->prepare("SELECT id, car_models FROM products WHERE car_models IS NOT NULL AND car_models != ''");
    $stmt->execute();
    $products = $stmt->fetchAll();

    foreach ($products as $product) {
        $productId = $product['id'];
        $carModelsStr = $product['car_models'];

        // 按逗号分割车型字符串
        $carModelList = explode(',', $carModelsStr);

        foreach ($carModelList as $carModelStr) {
            $parsed = parseCarModel($carModelStr, $brandMap);
            
            if ($parsed) {
                try {
                    // 在car_models表中查找或创建车型记录
                    $stmt = $pdo->prepare("SELECT id FROM car_models WHERE brand = ? AND model = ?");
                    $stmt->execute([$parsed['brand'], $parsed['model']]);
                    $carModel = $stmt->fetch();

                    if (!$carModel) {
                        // 创建新的车型记录
                        $stmt = $pdo->prepare("INSERT INTO car_models (brand, model) VALUES (?, ?) RETURNING id");
                        $stmt->execute([$parsed['brand'], $parsed['model']]);
                        $carModelId = $stmt->fetchColumn();
                    } else {
                        $carModelId = $carModel['id'];
                    }

                    // 创建产品车型关联记录（如果不存在）
                    $stmt = $pdo->prepare("INSERT INTO product_car_models (product_id, car_model_id) VALUES (?, ?) ON CONFLICT (product_id, car_model_id) DO NOTHING");
                    $stmt->execute([$productId, $carModelId]);

                    $migratedCount++;

                } catch (Exception $e) {
                    $errorCount++;
                    $errors[] = "产品ID {$productId}, 车型 '{$carModelStr}': " . $e->getMessage();
                }
            } else {
                $errorCount++;
                $errors[] = "产品ID {$productId}, 无法解析车型: '{$carModelStr}'";
            }
        }
    }

    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => '数据迁移完成',
        'migrated_count' => $migratedCount,
        'error_count' => $errorCount,
        'errors' => array_slice($errors, 0, 10) // 只返回前10个错误
    ]);

} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    echo json_encode([
        'success' => false,
        'message' => '数据迁移失败: ' . $e->getMessage()
    ]);
}
?>
