<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新闻详情 - 安徽春晟机械有限公司</title>
    <meta name="description" content="安徽春晟机械有限公司新闻详情">
    <meta name="keywords" content="春晟机械,新闻详情,减震器冲压件">
    
    <!-- 样式文件 -->
    <link href="pcstyle.css" rel="stylesheet" type="text/css">
    <link href="reset.css" rel="stylesheet" type="text/css">
    <link href="32_Pc_zh-CN.css" rel="stylesheet" type="text/css">
    <link href="css/custom.css" rel="stylesheet" type="text/css">
    <link href="css/customer-service-chat.css" rel="stylesheet" type="text/css">

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <!-- jQuery -->
    <script src="jquery-3.6.3.min.js"></script>

    <style>
        .news-detail-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .news-header-image {
            width: 100%;
            height: 300px;
            object-fit: cover;
        }

        .news-detail-content {
            padding: 40px;
        }

        .news-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .news-date {
            color: #be131b;
            font-weight: 600;
        }

        .news-category {
            background: #be131b;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
        }

        .news-title {
            font-size: 28px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            line-height: 1.4;
        }

        .news-tags {
            display: flex;
            gap: 8px;
            margin-bottom: 30px;
        }

        .news-tag {
            background: #f8f9fa;
            color: #666;
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 14px;
            border: 1px solid #e9ecef;
        }

        .news-content {
            color: #333;
            line-height: 1.8;
            font-size: 16px;
        }

        .news-content p {
            margin-bottom: 20px;
        }

        .news-content h3 {
            color: #be131b;
            margin: 30px 0 15px 0;
            font-size: 20px;
        }

        .back-button {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: #be131b;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-bottom: 30px;
        }

        .back-button:hover {
            background: #d32f2f;
            transform: translateY(-2px);
        }

        .related-news {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #e9ecef;
        }

        .related-news h3 {
            margin-bottom: 20px;
            color: #333;
        }

        .related-item {
            display: flex;
            gap: 15px;
            padding: 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .related-item:hover {
            background: #f8f9fa;
        }

        .related-image {
            width: 80px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
        }

        .related-content {
            flex: 1;
        }

        .related-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
            text-decoration: none;
        }

        .related-title:hover {
            color: #be131b;
        }

        .related-date {
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- 头部区域 -->
    <header class="header-section">
        <!-- Logo区域 -->
        <div class="logo-section" style="background-color: rgb(37, 37, 37); padding: 10px 0;">
            <div class="container" style="width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
                <div class="logo">
                    <img src="16792504.png" alt="安徽春晟机械有限公司" style="height: 60px;">
                </div>
                <div class="user-actions">
                    <a href="login.html" class="login-btn" style="color: white; margin-right: 15px;">登录</a>
                    <a href="register.html" class="register-btn" style="background: #be131b; color: white; padding: 5px 15px;">注册</a>
                </div>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <nav class="navigation" style="background-color: #F5F5F5; border-bottom: 1px solid #ddd;">
            <div class="container" style="width: 1200px; margin: 0 auto;">
                <ul class="nav-menu" style="display: flex; list-style: none; margin: 0; padding: 0;">
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="index.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">网站首页</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="about.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">关于我们</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">企业展示</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">工厂设备</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="products.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">产品中心</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="news.html" style="display: block; padding: 20px; color: white; background: #be131b; text-decoration: none; transition: all 0.3s;">新闻中心</a>
                    </li>
                    <li style="flex: 1; text-align: center;">
                        <a href="contact.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">联系我们</a>
                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- 新闻详情内容 -->
    <section class="news-detail-section" style="padding: 60px 0; background: #f8f8f8;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <a href="news.html" class="back-button">
                ← 返回新闻列表
            </a>

            <div id="news-detail-container" class="news-detail-container">
                <!-- 新闻详情将通过JavaScript动态加载 -->
                <div class="loading" style="text-align: center; padding: 60px;">
                    <div style="display: inline-block; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #be131b; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                    <p style="margin-top: 15px; color: #666;">正在加载新闻详情...</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer style="background: #333; color: white; padding: 40px 0;">
        <div class="container" style="width: 1200px; margin: 0 auto; text-align: center;">
            <p>&copy; 2024 安徽春晟机械有限公司. 保留所有权利.</p>
            <p>地址：安徽省广德经济开发区 | 专业从事减震器冲压件设计与生产</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/supabase-config.js"></script>
    <script src="js/main.js"></script>
    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    <script>

        // 新闻数据
        const newsData = {
            1: {
                id: 1,
                title: "春晟机械荣获\"优秀供应商\"称号",
                summary: "近日，安徽春晟机械有限公司凭借优质的产品质量和完善的服务体系，荣获多家知名汽车制造商颁发的\"优秀供应商\"称号。",
                content: `
                    <p>近日，安徽春晟机械有限公司传来喜讯，公司凭借优质的产品质量和完善的服务体系，荣获多家知名汽车制造商颁发的"优秀供应商"称号。这一荣誉的获得，充分体现了春晟机械在减震器冲压件领域的专业实力和市场认可度。</p>
                    
                    <h3>严格的质量管控体系</h3>
                    <p>春晟机械始终坚持"质量第一"的经营理念，建立了完善的质量管控体系。从原材料采购到产品出厂，每一个环节都严格按照ISO9001质量管理体系标准执行，确保产品质量的稳定性和可靠性。</p>
                    
                    <h3>先进的生产工艺</h3>
                    <p>公司引进了国际先进的冲压设备和检测仪器，采用精密的模具设计和制造工艺，能够生产出精度高、性能稳定的减震器冲压件产品。产品广泛应用于各类汽车减震器系统中。</p>
                    
                    <h3>持续的技术创新</h3>
                    <p>春晟机械注重技术研发和创新，与多家科研院所建立了长期合作关系，不断优化产品设计和生产工艺，提升产品的技术含量和附加值。</p>
                    
                    <h3>完善的服务体系</h3>
                    <p>公司建立了完善的客户服务体系，从产品设计、生产制造到售后服务，为客户提供全方位的技术支持和服务保障，赢得了客户的一致好评。</p>
                    
                    <p>此次荣获"优秀供应商"称号，是对春晟机械多年来坚持质量至上、服务至上理念的充分肯定。公司将以此为契机，继续加强质量管理，提升服务水平，为客户提供更加优质的产品和服务。</p>
                `,
                date: "2024-06-28",
                category: "company",
                categoryName: "公司动态",
                tags: ["荣誉", "供应商", "汽车制造"],
                image: "16719971.jpg"
            },
            2: {
                id: 2,
                title: "新型减震器冲压件生产线正式投产",
                summary: "春晟机械投资引进的全新自动化生产线正式投入使用，大幅提升了生产效率和产品质量稳定性。",
                content: `
                    <p>经过数月的设备调试和人员培训，春晟机械投资引进的全新自动化生产线正式投入使用。该生产线采用先进的数控技术和智能化管理系统，能够实现24小时连续生产，大幅提升了生产效率和产品质量稳定性。</p>

                    <h3>先进的自动化设备</h3>
                    <p>新生产线配备了最新一代的数控冲压设备，具有高精度、高效率的特点。设备采用伺服驱动系统，能够精确控制冲压力度和速度，确保产品尺寸精度和表面质量。</p>

                    <h3>智能化管理系统</h3>
                    <p>生产线集成了先进的MES制造执行系统，能够实时监控生产过程中的各项参数，自动记录生产数据，实现生产过程的全程可追溯。</p>

                    <h3>显著的效益提升</h3>
                    <p>新生产线投产后，产品生产效率提升了40%，产品合格率达到99.5%以上，大大降低了生产成本，提高了市场竞争力。</p>

                    <p>春晟机械将继续加大技术改造投入，不断提升生产自动化水平，为客户提供更加优质高效的产品和服务。</p>
                `,
                date: "2024-06-25",
                category: "product",
                categoryName: "产品更新",
                tags: ["生产线", "自动化", "技术升级"],
                image: "16719962.jpg"
            },
            3: {
                id: 3,
                title: "汽车零部件行业发展趋势分析",
                summary: "随着新能源汽车的快速发展，汽车零部件行业正面临新的机遇与挑战，轻量化、智能化成为发展主流。",
                content: `
                    <p>随着新能源汽车的快速发展和智能网联技术的不断进步，汽车零部件行业正面临着前所未有的机遇与挑战。轻量化、智能化、电动化已成为行业发展的主要趋势。</p>

                    <h3>轻量化技术成为关键</h3>
                    <p>为了提高燃油经济性和电动汽车续航里程，汽车轻量化技术越来越受到重视。高强度钢、铝合金、碳纤维等新材料的应用日益广泛，对冲压件的设计和制造提出了更高要求。</p>

                    <h3>智能制造推动产业升级</h3>
                    <p>工业4.0和智能制造技术的发展，推动汽车零部件制造向数字化、网络化、智能化方向发展。自动化生产线、机器人技术、人工智能等技术的应用，大大提升了生产效率和产品质量。</p>

                    <h3>新能源汽车带来新机遇</h3>
                    <p>新能源汽车市场的快速增长，为汽车零部件企业带来了新的发展机遇。电池包结构件、电机支架等新产品需求不断增长，为传统冲压件企业转型升级提供了新方向。</p>

                    <h3>环保要求日益严格</h3>
                    <p>随着环保法规的日益严格，汽车零部件企业必须加强环保投入，采用清洁生产工艺，减少污染排放，实现绿色可持续发展。</p>

                    <p>面对行业发展新趋势，春晟机械将继续加大研发投入，提升技术创新能力，为客户提供更加符合市场需求的高质量产品。</p>
                `,
                date: "2024-06-22",
                category: "industry",
                categoryName: "行业资讯",
                tags: ["行业分析", "新能源", "轻量化"],
                image: "16719937.jpg"
            }
        };

        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 渲染新闻详情
        function renderNewsDetail() {
            const newsId = getUrlParameter('id') || '1';
            const news = newsData[newsId];
            
            if (!news) {
                document.getElementById('news-detail-container').innerHTML = `
                    <div style="text-align: center; padding: 60px;">
                        <h2 style="color: #666;">新闻不存在</h2>
                        <p style="color: #999;">您访问的新闻可能已被删除或不存在</p>
                        <a href="news.html" class="back-button" style="margin-top: 20px;">返回新闻列表</a>
                    </div>
                `;
                return;
            }

            const detailHTML = `
                <img src="${news.image}" alt="${news.title}" class="news-header-image">
                <div class="news-detail-content">
                    <div class="news-meta">
                        <span class="news-date">${news.date}</span>
                        <span class="news-category">${news.categoryName}</span>
                    </div>
                    
                    <h1 class="news-title">${news.title}</h1>
                    
                    <div class="news-tags">
                        ${news.tags.map(tag => `<span class="news-tag">${tag}</span>`).join('')}
                    </div>
                    
                    <div class="news-content">
                        ${news.content}
                    </div>
                </div>
            `;

            document.getElementById('news-detail-container').innerHTML = detailHTML;
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderNewsDetail();
        });
    </script>

    <!-- 客服系统脚本 -->
    <script src="js/customer-service-chat.js"></script>
</body>
</html>
