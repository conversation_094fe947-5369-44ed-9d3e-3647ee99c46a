// 基于自定义Users表的认证系统
class CustomAuth {
    constructor() {
        this.supabase = null;
        this.currentUser = null;
        this.userType = 'guest';
        this.isInitialized = false;
        this.sessionToken = null;

        // 用户权限类型
        this.USER_TYPES = {
            GUEST: 'guest',           // 游客 - 基本浏览权限
            NORMAL: 'normal',         // 普通用户 - 新注册用户默认权限
            PREMIUM: 'premium',       // 高级用户 - 可查看详情和下载基础资料
            PRIVILEGED: 'privileged', // 特许用户 - 可下载PDF资料
            ADMIN: 'admin'            // 管理员 - 完全权限
        };

        this.init();
    }

    async init() {
        console.log('🔐 [CUSTOM-AUTH] 开始初始化认证系统');

        // 等待Supabase客户端初始化
        await this.waitForSupabase();

        // 检查当前认证状态
        await this.checkAuthStatus();

        this.isInitialized = true;
        console.log('🔐 [CUSTOM-AUTH] 认证系统初始化完成');

        // 触发初始化完成事件
        window.dispatchEvent(new CustomEvent('authReady', {
            detail: {
                user: this.currentUser,
                userType: this.userType
            }
        }));
    }

    async waitForSupabase() {
        let retryCount = 0;
        while (retryCount < 30) {
            if (window.supabaseClient) {
                this.supabase = window.supabaseClient;
                console.log('🔐 [CUSTOM-AUTH] Supabase客户端已连接');
                return;
            }
            await new Promise(resolve => setTimeout(resolve, 200));
            retryCount++;
        }
        throw new Error('Supabase客户端初始化超时');
    }

    // 生成会话令牌
    generateSessionToken() {
        return btoa(Date.now() + '_' + Math.random().toString(36).substring(2, 11));
    }

    // 密码哈希
    hashPassword(password) {
        return btoa(password + 'chunsheng_salt_2024');
    }

    // 验证密码
    verifyPassword(password, hashedPassword) {
        return this.hashPassword(password) === hashedPassword;
    }

    async checkAuthStatus() {
        console.log('🔐 [CUSTOM-AUTH] 检查认证状态');

        // 这里不依赖Supabase Auth，而是检查我们自己的会话
        const savedSession = sessionStorage.getItem('auth_session');
        const savedUser = sessionStorage.getItem('auth_user');

        if (savedSession && savedUser) {
            try {
                const user = JSON.parse(savedUser);
                const sessionData = JSON.parse(savedSession);

                // 检查会话是否过期（8小时）
                const now = Date.now();
                const sessionAge = now - sessionData.timestamp;
                const maxAge = 8 * 60 * 60 * 1000; // 8小时

                if (sessionAge < maxAge) {
                    this.currentUser = user;
                    this.userType = user.user_type || this.USER_TYPES.PREMIUM;
                    this.sessionToken = sessionData.token;

                    console.log('🔐 [CUSTOM-AUTH] 恢复会话:', user.username);
                    this.updateUI();
                    return;
                }
            } catch (error) {
                console.error('🔐 [CUSTOM-AUTH] 会话数据解析失败:', error);
            }
        }

        // 清除无效会话
        this.clearSession();
    }

    // 创建会话
    createSession(user) {
        const sessionData = {
            token: this.generateSessionToken(),
            timestamp: Date.now(),
            userId: user.id
        };

        this.sessionToken = sessionData.token;
        this.currentUser = user;
        this.userType = user.user_type || this.USER_TYPES.PREMIUM;

        // 保存到sessionStorage（关闭浏览器后失效）
        sessionStorage.setItem('auth_session', JSON.stringify(sessionData));
        sessionStorage.setItem('auth_user', JSON.stringify(user));

        console.log('🔐 [CUSTOM-AUTH] 会话已创建:', user.username);

        // 更新访问统计
        this.updateUserVisitStats(user.id);

        this.updateUI();
    }

    // 清除会话
    clearSession() {
        this.sessionToken = null;
        this.currentUser = null;
        this.userType = this.USER_TYPES.GUEST;

        sessionStorage.removeItem('auth_session');
        sessionStorage.removeItem('auth_user');

        console.log('🔐 [CUSTOM-AUTH] 会话已清除');
        this.updateUI();
    }

    // 用户登录
    async signIn(email, password) {
        if (!this.supabase) {
            throw new Error('Supabase未初始化');
        }

        try {
            console.log('🔐 [CUSTOM-AUTH] 开始登录:', email);

            // 查询用户
            const { data: user, error } = await this.supabase
                .from('users')
                .select('*')
                .eq('email', email)
                .eq('is_active', true)
                .single();

            if (error) {
                console.error('🔐 [CUSTOM-AUTH] 用户查询失败:', error);
                if (error.code === 'PGRST116') {
                    throw new Error('用户不存在或已被禁用');
                }
                throw new Error('查询用户失败: ' + error.message);
            }

            if (!user) {
                throw new Error('用户不存在或已被禁用');
            }

            if (!user.password_hash) {
                throw new Error('用户密码未设置，请联系管理员');
            }

            // 验证密码
            if (!this.verifyPassword(password, user.password_hash)) {
                throw new Error('密码错误');
            }

            // 创建会话
            this.createSession(user);

            console.log('🔐 [CUSTOM-AUTH] 登录成功:', user.username);
            return { success: true, user: user };

        } catch (error) {
            console.error('🔐 [CUSTOM-AUTH] 登录过程出错:', error);
            throw error;
        }
    }

    // 用户注册
    async signUp(userData) {
        if (!this.supabase) {
            throw new Error('Supabase未初始化');
        }

        try {
            console.log('🔐 [CUSTOM-AUTH] 开始注册:', userData.email);

            // 检查用户是否已存在
            const { data: existingUser } = await this.supabase
                .from('users')
                .select('email')
                .eq('email', userData.email)
                .single();

            if (existingUser) {
                throw new Error('该邮箱已被注册');
            }

            // 创建新用户
            const newUser = {
                username: userData.username,
                email: userData.email,
                password_hash: this.hashPassword(userData.password),
                user_type: userData.userType || this.USER_TYPES.PREMIUM,
                company_name: userData.company || '',
                phone: userData.phone || '',
                first_name: userData.firstName || '',
                last_name: userData.lastName || '',
                is_active: true
            };

            const { data: user, error } = await this.supabase
                .from('users')
                .insert([newUser])
                .select()
                .single();

            if (error) {
                throw new Error('注册失败: ' + error.message);
            }

            console.log('🔐 [CUSTOM-AUTH] 注册成功:', user.username);
            return { success: true, user: user };

        } catch (error) {
            console.error('🔐 [CUSTOM-AUTH] 注册过程出错:', error);
            throw error;
        }
    }

    // 用户登出
    async signOut() {
        console.log('🔐 [CUSTOM-AUTH] 开始登出');
        this.clearSession();
        return { success: true, message: '登出成功' };
    }

    // 管理员重置密码（通过邮箱）
    async adminResetPasswordByEmail(email, newPassword) {
        if (!this.isAdmin()) {
            throw new Error('权限不足：只有管理员可以重置密码');
        }

        try {
            console.log('🔐 [CUSTOM-AUTH] 管理员重置密码:', email);

            const { error } = await this.supabase
                .from('users')
                .update({ password_hash: this.hashPassword(newPassword) })
                .eq('email', email);

            if (error) {
                throw new Error('密码重置失败: ' + error.message);
            }

            console.log('🔐 [CUSTOM-AUTH] 密码重置成功:', email);
            return { success: true, message: '密码重置成功' };

        } catch (error) {
            console.error('🔐 [CUSTOM-AUTH] 管理员重置密码失败:', error);
            throw error;
        }
    }

    // 管理员重置密码（通过用户ID）
    async adminResetPassword(userId, newPassword) {
        if (!this.isAdmin()) {
            throw new Error('权限不足：只有管理员可以重置密码');
        }

        try {
            const { error } = await this.supabase
                .from('users')
                .update({ password_hash: this.hashPassword(newPassword) })
                .eq('id', userId);

            if (error) {
                throw new Error('密码重置失败: ' + error.message);
            }

            return { success: true, message: '密码重置成功' };

        } catch (error) {
            console.error('🔐 [CUSTOM-AUTH] 管理员重置密码失败:', error);
            throw error;
        }
    }

    // 权限检查方法
    canViewBasicDetails() {
        return [this.USER_TYPES.NORMAL, this.USER_TYPES.PREMIUM, this.USER_TYPES.PRIVILEGED, this.USER_TYPES.ADMIN].includes(this.userType);
    }

    canViewDetails() {
        return [this.USER_TYPES.PREMIUM, this.USER_TYPES.PRIVILEGED, this.USER_TYPES.ADMIN].includes(this.userType);
    }

    canDownloadBasic() {
        return [this.USER_TYPES.PREMIUM, this.USER_TYPES.PRIVILEGED, this.USER_TYPES.ADMIN].includes(this.userType);
    }

    canDownloadPDF() {
        return [this.USER_TYPES.PRIVILEGED, this.USER_TYPES.ADMIN].includes(this.userType);
    }

    canDownloadAll() {
        return this.userType === this.USER_TYPES.ADMIN;
    }

    canAccessAdmin() {
        return this.userType === this.USER_TYPES.ADMIN;
    }

    // 获取用户信息
    getCurrentUser() {
        return this.currentUser;
    }

    getUserType() {
        return this.userType;
    }

    isLoggedIn() {
        return this.currentUser !== null;
    }

    isAdmin() {
        return this.userType === this.USER_TYPES.ADMIN;
    }

    // 更新用户访问统计
    async updateUserVisitStats(userId) {
        try {
            console.log('📊 [CUSTOM-AUTH] 更新用户访问统计:', userId);

            // 先获取当前访问次数
            const { data: currentUser, error: fetchError } = await this.supabase
                .from('users')
                .select('visit_count')
                .eq('id', userId)
                .single();

            if (fetchError) {
                console.error('获取当前访问次数失败:', fetchError);
                return;
            }

            const currentCount = currentUser?.visit_count || 0;

            // 更新访问次数和最近访问时间
            const { error } = await this.supabase
                .from('users')
                .update({
                    visit_count: currentCount + 1,
                    last_visit_at: new Date().toISOString()
                })
                .eq('id', userId);

            if (error) {
                console.error('更新访问统计失败:', error);
            } else {
                console.log('✅ [CUSTOM-AUTH] 访问统计更新成功，新访问次数:', currentCount + 1);
            }
        } catch (error) {
            console.error('更新访问统计异常:', error);
        }
    }

    // 更新UI显示
    updateUI() {
        this.updateUserActions();
        this.updatePermissionElements();
    }

    updateUserActions() {
        const userActions = document.querySelectorAll('.user-actions');
        
        userActions.forEach(container => {
            if (this.isLoggedIn()) {
                // 已登录状态
                container.innerHTML = `
                    <span class="user-info" style="color: white; margin-right: 15px;">
                        欢迎，${this.currentUser.username}
                    </span>
                    <button class="logout-btn" onclick="window.auth.signOut()" 
                            style="background: #be131b; color: white; padding: 5px 15px; border: none; cursor: pointer;">
                        退出
                    </button>
                `;
            } else {
                // 未登录状态
                container.innerHTML = `
                    <a href="login.html" class="login-btn" style="color: white; margin-right: 15px;">登录</a>
                    <a href="register.html" class="register-btn" style="background: #be131b; color: white; padding: 5px 15px;">注册</a>
                `;
            }
        });
    }

    updatePermissionElements() {
        // 更新权限相关的UI元素
        const detailButtons = document.querySelectorAll('.view-details-btn');
        detailButtons.forEach(btn => {
            btn.style.display = this.canViewDetails() ? 'block' : 'none';
        });

        const downloadButtons = document.querySelectorAll('.download-btn');
        downloadButtons.forEach(btn => {
            const level = btn.dataset.level;
            if (level === 'basic') {
                btn.style.display = this.canDownloadBasic() ? 'block' : 'none';
            } else {
                btn.style.display = this.canDownloadAll() ? 'block' : 'none';
            }
        });

        const adminElements = document.querySelectorAll('.admin-only');
        adminElements.forEach(element => {
            element.style.display = this.canAccessAdmin() ? 'block' : 'none';
        });
    }
}

// 创建全局认证实例
window.auth = new CustomAuth();

console.log('🔐 [CUSTOM-AUTH] 认证系统已加载');
