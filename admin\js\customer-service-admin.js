// 简化的客服管理功能

let allUsers = [];
let selectedUser = null; // 改名避免与全局currentUser冲突
let currentUserMessages = [];
let messagesSubscription = null;

// 创建管理员专用的Supabase客户端（绕过RLS限制）
let adminSupabase = null;

// 初始化管理员客户端
function initAdminSupabase() {
    // 对于管理员操作，我们需要使用不同的方法来绕过RLS
    // 这里我们先尝试使用普通客户端，但添加管理员身份验证
    adminSupabase = supabase;
    console.log('管理员Supabase客户端已初始化');
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    try {
        const isAuthenticated = await checkAdminAuth();
        if (isAuthenticated) {
            initAdminSupabase();
            initializeCustomerServicePage();
        }
    } catch (error) {
        console.error('认证检查失败:', error);
        // 即使认证失败，也尝试初始化页面（用于测试）
        initAdminSupabase();
        initializeCustomerServicePage();
    }
});

// 初始化客服页面
async function initializeCustomerServicePage() {
    await loadUsersWithMessages();
    setupRealtimeSubscriptions();
}

// 简化版本 - 不再需要统计数据
// 统计功能已移除，专注于消息管理

// 加载有消息的用户列表
async function loadUsersWithMessages() {
    try {
        const { data: messages, error } = await supabase
            .from('customer_service_messages')
            .select(`
                user_id,
                user_name,
                user_email,
                message_content,
                message_type,
                is_read,
                created_at
            `)
            .order('created_at', { ascending: false });

        if (error) throw error;

        // 按用户分组消息
        const userMap = new Map();
        messages?.forEach(message => {
            // 为null的user_id创建一个唯一标识（使用安全的字符）
            let userId = message.user_id;
            if (!userId) {
                // 创建一个基于邮箱或姓名的稳定ID
                const identifier = message.user_email || message.user_name || 'unknown';
                // 移除特殊字符，只保留字母数字和下划线
                const safeIdentifier = identifier.replace(/[^a-zA-Z0-9_]/g, '_');
                // 使用简单的哈希来确保稳定性
                const hash = safeIdentifier.split('').reduce((a, b) => {
                    a = ((a << 5) - a) + b.charCodeAt(0);
                    return a & a;
                }, 0);
                userId = `guest_${Math.abs(hash)}`;
            }

            if (!userMap.has(userId)) {
                userMap.set(userId, {
                    user_id: userId,
                    original_user_id: message.user_id, // 保存原始user_id
                    user_name: message.user_name || '未知用户',
                    user_email: message.user_email || '',
                    last_message_time: message.created_at,
                    unread_count: 0,
                    messages: []
                });
            }

            const user = userMap.get(userId);
            user.messages.push(message);

            // 计算未读消息数（用户发送的未读消息）
            if (message.message_type === 'user' && !message.is_read) {
                user.unread_count++;
            }
        });

        allUsers = Array.from(userMap.values())
            .sort((a, b) => new Date(b.last_message_time) - new Date(a.last_message_time));

        displayUsers();

    } catch (error) {
        console.error('加载用户消息失败:', error);
        showError('加载用户数据失败');
    }
}

// 显示用户列表
function displayUsers() {
    const usersList = document.getElementById('users-list');

    if (allUsers.length === 0) {
        usersList.innerHTML = '<div class="no-data">暂无咨询用户</div>';
        return;
    }

    const usersHtml = allUsers.map(user => {
        const timeAgo = getTimeAgo(user.last_message_time);
        const hasUnread = user.unread_count > 0;

        return `
            <div class="user-item ${selectedUser?.user_id === user.user_id ? 'active' : ''} ${hasUnread ? 'has-unread' : ''}"
                 onclick="selectUser('${user.user_id}')">
                <div class="user-info">
                    <h4>${user.user_name || '未知用户'}</h4>
                    <p>${user.user_email || ''}</p>
                    <p>${timeAgo} ${hasUnread ? `(${user.unread_count}条未读)` : ''}</p>
                </div>
            </div>
        `;
    }).join('');

    usersList.innerHTML = usersHtml;
}

// 选择用户
async function selectUser(userId) {
    try {
        console.log('选择用户:', userId);
        const user = allUsers.find(u => u.user_id === userId);
        if (!user) {
            console.error('未找到用户:', userId);
            return;
        }

        console.log('找到用户:', user);
        selectedUser = user;
        currentUserMessages = user.messages;
        console.log('用户消息数量:', currentUserMessages.length);

        // 加载并显示聊天界面
        await loadChatInterface(user);

        // 标记用户消息为已读（使用原始user_id）
        const markedMessages = await markUserMessagesAsRead(user.original_user_id);

    } catch (error) {
        console.error('选择用户失败:', error);
    }
}

// 加载聊天界面
async function loadChatInterface(user) {
    const chatContainer = document.getElementById('chat-container');

    // 创建聊天界面
    chatContainer.innerHTML = `
        <div class="chat-header">
            <h4>${user.user_name || '未知用户'}</h4>
            <div>
                <span>${user.user_email || ''}</span>
            </div>
        </div>
        <div class="chat-messages" id="chat-messages">
            <!-- 消息将在这里显示 -->
        </div>
        <div class="chat-input">
            <input type="text" id="message-input" placeholder="输入回复消息..."
                   onkeypress="handleMessageKeyPress(event, '${user.user_id}')">
            <button onclick="sendReply('${user.user_id}')">发送</button>
        </div>
    `;

    // 加载历史消息
    displayMessages(currentUserMessages);
}

// 显示消息
function displayMessages(messages) {
    console.log('显示消息:', messages);
    const messagesContainer = document.getElementById('chat-messages');
    console.log('消息容器:', messagesContainer);

    if (!messagesContainer) {
        console.error('找不到消息容器元素 #chat-messages');
        return;
    }

    if (!messages || messages.length === 0) {
        console.log('没有消息要显示');
        messagesContainer.innerHTML = '<div class="no-session">暂无消息记录</div>';
        return;
    }

    // 按时间排序消息（最早的在上面）
    const sortedMessages = [...messages].sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

    const messagesHtml = sortedMessages.map(message => {
        // 直接使用message_type作为CSS类名，因为我们已经在CSS中定义了对应的样式
        const messageClass = message.message_type || 'user';

        const time = new Date(message.created_at).toLocaleTimeString();
        console.log('处理消息:', message);

        return `
            <div class="message ${messageClass}">
                <div class="message-content">
                    ${message.message_content}
                    <div class="message-time">${time}</div>
                </div>
            </div>
        `;
    }).join('');

    console.log('生成的HTML:', messagesHtml);
    messagesContainer.innerHTML = messagesHtml;
    console.log('设置HTML后的容器内容:', messagesContainer.innerHTML);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// 发送回复消息
async function sendReply() {
    const messageInput = document.getElementById('message-input');
    const messageContent = messageInput.value.trim();

    if (!messageContent) return;

    try {
        const { data, error } = await supabase
            .from('customer_service_messages')
            .insert([{
                user_id: selectedUser.original_user_id, // 使用原始user_id
                user_name: selectedUser.user_name,
                user_email: selectedUser.user_email,
                message_content: messageContent,
                message_type: 'admin',
                is_read: false
            }])
            .select()
            .single();

        if (error) throw error;

        messageInput.value = '';

        // 添加到当前消息列表和用户消息列表
        currentUserMessages.push(data);
        selectedUser.messages.push(data);

        // 更新最后消息时间
        selectedUser.last_message_time = data.created_at;

        // 显示更新后的消息
        displayMessages(currentUserMessages);

        // 更新用户列表显示（不重新加载数据）
        displayUsers();

    } catch (error) {
        console.error('发送回复失败:', error);
        showError('发送回复失败');
    }
}

// 处理回车键发送消息
function handleMessageKeyPress(event, userId) {
    if (event.key === 'Enter') {
        sendReply(userId);
    }
}

// 搜索用户
function searchUsers() {
    const searchInput = document.getElementById('user-search');
    const searchTerm = searchInput.value.toLowerCase().trim();

    if (!searchTerm) {
        displayUsers();
        return;
    }

    const filteredUsers = allUsers.filter(user =>
        (user.user_name && user.user_name.toLowerCase().includes(searchTerm)) ||
        (user.user_email && user.user_email.toLowerCase().includes(searchTerm))
    );

    const usersList = document.getElementById('users-list');

    if (filteredUsers.length === 0) {
        usersList.innerHTML = '<div class="no-data">未找到匹配的用户</div>';
        return;
    }

    const usersHtml = filteredUsers.map(user => {
        const timeAgo = getTimeAgo(user.last_message_time);
        const hasUnread = user.unread_count > 0;

        return `
            <div class="user-item ${selectedUser?.user_id === user.user_id ? 'active' : ''} ${hasUnread ? 'has-unread' : ''}"
                 onclick="selectUser('${user.user_id}')">
                <div class="user-info">
                    <h4>${user.user_name || '未知用户'}</h4>
                    <p>${user.user_email || ''}</p>
                    <p>${timeAgo} ${hasUnread ? `(${user.unread_count}条未读)` : ''}</p>
                </div>
            </div>
        `;
    }).join('');

    usersList.innerHTML = usersHtml;
}

// 设置实时订阅
function setupRealtimeSubscriptions() {
    // 订阅新消息
    messagesSubscription = supabase
        .channel('admin_customer_service_messages')
        .on('postgres_changes',
            { event: 'INSERT', schema: 'public', table: 'customer_service_messages' },
            (payload) => {
                const newMessage = payload.new;

                // 如果是用户发送的消息，更新界面
                if (newMessage.message_type === 'user') {
                    // 重新加载用户列表
                    loadUsersWithMessages();

                    // 如果当前正在查看该用户的对话，更新消息
                    // 需要检查原始user_id或生成的guest_id
                    const isCurrentUser = selectedUser && (
                        selectedUser.original_user_id === newMessage.user_id ||
                        (selectedUser.original_user_id === null && newMessage.user_id === null)
                    );

                    if (isCurrentUser) {
                        currentUserMessages.push(newMessage);
                        selectedUser.messages.push(newMessage);
                        displayMessages(currentUserMessages);
                    }
                }
            }
        )
        .subscribe();
}

// 标记用户消息为已读的API函数
async function markUserMessagesAsRead(originalUserId) {
    try {
        console.log('标记消息已读，原始用户ID:', originalUserId);

        // 构建更新查询
        let updateQuery = supabase
            .from('customer_service_messages')
            .update({ is_read: true })
            .eq('message_type', 'user')
            .eq('is_read', false);

        // 如果originalUserId为null，需要特殊处理
        if (originalUserId === null || originalUserId === undefined) {
            console.log('使用null查询条件');
            updateQuery = updateQuery.is('user_id', null);
        } else {
            console.log('使用用户ID查询条件:', originalUserId);
            updateQuery = updateQuery.eq('user_id', originalUserId);
        }

        // 先查询要更新的消息
        let checkQuery = supabase
            .from('customer_service_messages')
            .select('*')
            .eq('message_type', 'user')
            .eq('is_read', false);

        if (originalUserId === null || originalUserId === undefined) {
            checkQuery = checkQuery.is('user_id', null);
        } else {
            checkQuery = checkQuery.eq('user_id', originalUserId);
        }

        const { data: checkData, error: checkError } = await checkQuery;
        console.log('查询到的未读消息:', checkData, '错误:', checkError);

        if (checkData && checkData.length > 0) {
            console.log(`准备标记 ${checkData.length} 条消息为已读`);
            console.log('未读消息详情:', checkData);

            // 使用消息ID直接更新，避免查询条件问题
            const messageIds = checkData.map(msg => msg.id);
            console.log('要更新的消息ID:', messageIds);
            console.log('消息详情:', checkData.map(msg => ({ id: msg.id, is_read: msg.is_read, message_type: msg.message_type })));

            const { data, error } = await supabase
                .from('customer_service_messages')
                .update({ is_read: true })
                .in('id', messageIds)
                .select('*');

            console.log('标记已读结果:', { data, error });

            if (error) {
                console.error('更新操作错误:', error);
            } else if (data && data.length === 0) {
                console.warn('更新操作没有找到匹配的记录，可能是权限问题或记录已被删除');
                // 尝试直接查询这些ID是否存在
                const { data: existCheck } = await supabase
                    .from('customer_service_messages')
                    .select('id, is_read, message_type, user_id')
                    .in('id', messageIds);
                console.log('ID存在性检查:', existCheck);

                // 尝试使用RPC函数来绕过RLS限制
                console.log('尝试使用RPC函数更新消息...');
                try {
                    const { data: rpcResult, error: rpcError } = await supabase.rpc('mark_messages_as_read', {
                        message_ids: messageIds
                    });

                    if (rpcError) {
                        console.error('RPC更新失败:', rpcError);
                        // 如果RPC失败，尝试直接更新（可能需要管理员权限）
                        console.log('尝试直接更新...');
                        return await attemptDirectUpdate(messageIds);
                    } else {
                        console.log('RPC更新成功:', rpcResult);
                        return rpcResult;
                    }
                } catch (rpcErr) {
                    console.error('RPC调用异常:', rpcErr);
                    // 如果RPC不存在，尝试直接更新
                    console.log('RPC不存在，尝试直接更新...');
                    return await attemptDirectUpdate(messageIds);
                }
            }

            if (error) throw error;

            console.log('成功标记', data?.length || 0, '条消息为已读');

            // 更新本地用户数据的未读计数
            if (selectedUser) {
                selectedUser.unread_count = 0;
                // 更新用户列表显示
                displayUsers();
            }

            return data;
        } else {
            console.log('没有找到需要标记为已读的消息');
            return [];
        }
    } catch (error) {
        console.error('标记消息已读失败:', error);
        return null;
    }
}

// 尝试直接更新消息（绕过RLS）
async function attemptDirectUpdate(messageIds) {
    console.log('尝试直接更新消息，绕过RLS限制...');
    let updateCount = 0;

    for (const msgId of messageIds) {
        try {
            // 尝试使用原始SQL更新
            const { data: directUpdate, error: directError } = await supabase
                .from('customer_service_messages')
                .update({ is_read: true })
                .eq('id', msgId)
                .eq('message_type', 'user')  // 确保只更新用户消息
                .eq('is_read', false)        // 确保只更新未读消息
                .select('*');

            if (directError) {
                console.error(`直接更新消息 ${msgId} 失败:`, directError);
            } else if (directUpdate && directUpdate.length > 0) {
                updateCount++;
                console.log(`成功直接更新消息 ${msgId}`);
            } else {
                console.warn(`消息 ${msgId} 直接更新返回空结果`);

                // 最后尝试：强制更新，不检查条件
                const { data: forceUpdate, error: forceError } = await supabase
                    .from('customer_service_messages')
                    .update({ is_read: true })
                    .eq('id', msgId)
                    .select('*');

                if (forceError) {
                    console.error(`强制更新消息 ${msgId} 失败:`, forceError);
                } else if (forceUpdate && forceUpdate.length > 0) {
                    updateCount++;
                    console.log(`成功强制更新消息 ${msgId}`);
                } else {
                    console.error(`消息 ${msgId} 所有更新方法都失败`);
                }
            }
        } catch (err) {
            console.error(`更新消息 ${msgId} 异常:`, err);
        }
    }

    console.log(`直接更新完成，成功更新 ${updateCount} 条消息`);
    return updateCount > 0 ? Array(updateCount).fill({ updated: true }) : [];
}

// 获取时间差
function getTimeAgo(dateString) {
    const now = new Date();
    const date = new Date(dateString);
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;

    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}小时前`;

    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}天前`;
}

// 页面卸载时清理订阅
window.addEventListener('beforeunload', function() {
    if (messagesSubscription) {
        supabase.removeChannel(messagesSubscription);
    }
});
