<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 数据库配置
$host = 'aws-0-ap-southeast-1.pooler.supabase.com';
$port = '6543';
$dbname = 'postgres';
$username = 'postgres.snckktsqwrbfwtjlvcfr';
$password = 'Mm@124578';

try {
    $dsn = "pgsql:host=$host;port=$port;dbname=$dbname";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);

    // 创建产品车型关联表
    $sql = "CREATE TABLE IF NOT EXISTS product_car_models (
        id SERIAL PRIMARY KEY,
        product_id INTEGER NOT NULL,
        car_model_id INTEGER NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_product_car_models_product_id 
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
        CONSTRAINT fk_product_car_models_car_model_id 
            FOREIGN KEY (car_model_id) REFERENCES car_models(id) ON DELETE CASCADE,
        CONSTRAINT unique_product_car_model 
            UNIQUE (product_id, car_model_id)
    )";
    
    $pdo->exec($sql);
    
    echo json_encode([
        'success' => true,
        'message' => '产品车型关联表创建成功'
    ]);

} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => '数据库错误: ' . $e->getMessage()
    ]);
}
?>
