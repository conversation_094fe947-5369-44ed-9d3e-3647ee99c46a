// 智能搜索功能

// 特殊符号映射
const SPECIAL_SYMBOLS = {
    'φ': ['phi', 'Φ', 'ф', 'diameter', '直径', '外径', '内径'],
    '×': ['x', 'X', '*', '乘', '乘以'],
    '°': ['度', 'degree', 'deg'],
    '±': ['+-', '±', '正负', '误差'],
    'Ø': ['φ', 'diameter', '直径'],
    '∅': ['φ', 'diameter', '直径']
};

// 数字近似搜索范围
const NUMERIC_TOLERANCE = 3; // 默认误差范围

// 智能搜索类
class SmartSearch {
    constructor(options = {}) {
        this.products = [];
        this.searchHistory = [];
        this.defaultTolerance = options.defaultTolerance || NUMERIC_TOLERANCE;
        this.maxHistorySize = options.maxHistorySize || 50;
    }
    
    // 设置产品数据
    setProducts(products) {
        this.products = products;
    }

    // 设置默认误差范围
    setDefaultTolerance(tolerance) {
        this.defaultTolerance = tolerance;
    }

    // 获取当前默认误差范围
    getDefaultTolerance() {
        return this.defaultTolerance;
    }
    
    // 主搜索函数
    search(query, options = {}) {
        const {
            tolerance = this.defaultTolerance,
            includeApproximate = true,
            caseSensitive = false
        } = options;
        
        if (!query || query.trim() === '') {
            return {
                exact: this.products,
                approximate: [],
                suggestions: []
            };
        }
        
        const normalizedQuery = this.normalizeQuery(query, caseSensitive);
        
        // 记录搜索历史
        this.addToHistory(query);
        
        // 执行搜索
        const exactMatches = this.findExactMatches(normalizedQuery, caseSensitive);
        const approximateMatches = includeApproximate ? 
            this.findApproximateMatches(normalizedQuery, tolerance, exactMatches) : [];
        const suggestions = this.generateSuggestions(normalizedQuery);
        
        return {
            exact: exactMatches,
            approximate: approximateMatches,
            suggestions: suggestions,
            query: normalizedQuery
        };
    }
    
    // 标准化查询字符串
    normalizeQuery(query, caseSensitive = false) {
        let normalized = caseSensitive ? query : query.toLowerCase();
        
        // 处理特殊符号
        Object.keys(SPECIAL_SYMBOLS).forEach(symbol => {
            const alternatives = SPECIAL_SYMBOLS[symbol];
            alternatives.forEach(alt => {
                const regex = new RegExp(alt.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
                normalized = normalized.replace(regex, symbol);
            });
        });
        
        return normalized.trim();
    }
    
    // 查找精确匹配
    findExactMatches(query, caseSensitive = false) {
        return this.products.filter(product => {
            const searchFields = [
                product.product_name,
                product.specifications,
                product.material,
                product.thickness,
                product.stock_code,
                product.data_id,
                product.product_category,
                product.shape_code,
                product.main_process_1,
                product.main_process_2,
                product.main_process_3,
                product.main_process_4,
                product.variable_process_1,
                product.variable_process_2,
                product.variable_process_3,
                product.remarks
            ].filter(field => field); // 过滤空值

            return searchFields.some(field => {
                // 简化匹配逻辑：直接比较标准化后的字符串
                const normalizedField = this.normalizeQuery(field, caseSensitive);

                // 检查完全匹配或包含匹配
                return normalizedField === query || normalizedField.includes(query);
            });
        });
    }
    
    // 查找近似匹配
    findApproximateMatches(query, tolerance, exactMatches) {
        const exactIds = new Set(exactMatches.map(p => p.data_id || p.id));
        const approximateMatches = [];
        
        // 提取查询中的数字
        const queryNumbers = this.extractNumbers(query);
        
        if (queryNumbers.length === 0) {
            return approximateMatches;
        }
        
        this.products.forEach(product => {
            if (exactIds.has(product.data_id || product.id)) return; // 跳过已经精确匹配的
            
            const searchFields = [
                product.specifications,
                product.thickness,
                product.shape_code,
                product.main_process_1,
                product.main_process_2,
                product.main_process_3,
                product.main_process_4
            ].filter(field => field);
            
            let hasApproximateMatch = false;
            let matchDetails = [];
            
            searchFields.forEach(field => {
                const fieldNumbers = this.extractNumbers(field);
                
                queryNumbers.forEach(queryNum => {
                    fieldNumbers.forEach(fieldNum => {
                        const difference = Math.abs(queryNum - fieldNum);
                        if (difference > 0 && difference <= tolerance) {
                            hasApproximateMatch = true;
                            matchDetails.push({
                                field: field,
                                queryValue: queryNum,
                                fieldValue: fieldNum,
                                difference: difference
                            });
                        }
                    });
                });
            });
            
            if (hasApproximateMatch) {
                approximateMatches.push({
                    ...product,
                    matchDetails: matchDetails,
                    relevanceScore: this.calculateRelevanceScore(matchDetails)
                });
            }
        });
        
        // 按相关性排序
        return approximateMatches.sort((a, b) => b.relevanceScore - a.relevanceScore);
    }
    
    // 提取字符串中的数字
    extractNumbers(text) {
        if (!text) return [];
        
        // 匹配整数和小数
        const numberRegex = /\d+\.?\d*/g;
        const matches = text.match(numberRegex);
        
        return matches ? matches.map(num => parseFloat(num)) : [];
    }
    
    // 计算相关性分数
    calculateRelevanceScore(matchDetails) {
        if (!matchDetails || matchDetails.length === 0) return 0;
        
        let score = 0;
        matchDetails.forEach(detail => {
            // 差值越小，分数越高
            const differenceScore = Math.max(0, 10 - detail.difference);
            score += differenceScore;
        });
        
        return score / matchDetails.length;
    }
    
    // 生成搜索建议
    generateSuggestions(query) {
        const suggestions = new Set();
        
        // 基于搜索历史的建议
        this.searchHistory.forEach(historyItem => {
            if (historyItem.toLowerCase().includes(query.toLowerCase()) && historyItem !== query) {
                suggestions.add(historyItem);
            }
        });
        
        // 基于产品数据的建议
        this.products.forEach(product => {
            [product.specifications, product.material, product.product_category].forEach(field => {
                if (field && field.toLowerCase().includes(query.toLowerCase())) {
                    // 提取相关的词汇作为建议
                    const words = field.split(/[\s×φ\-]/);
                    words.forEach(word => {
                        if (word.length > 1 && word.toLowerCase().includes(query.toLowerCase())) {
                            suggestions.add(word);
                        }
                    });
                }
            });
        });
        
        return Array.from(suggestions).slice(0, 10); // 限制建议数量
    }
    
    // 添加到搜索历史
    addToHistory(query) {
        if (query && query.trim() !== '') {
            this.searchHistory.unshift(query.trim());
            // 保持历史记录在合理范围内
            if (this.searchHistory.length > 50) {
                this.searchHistory = this.searchHistory.slice(0, 50);
            }
            // 去重
            this.searchHistory = [...new Set(this.searchHistory)];
        }
    }
    
    // 获取搜索历史
    getSearchHistory() {
        return this.searchHistory;
    }
    
    // 清除搜索历史
    clearHistory() {
        this.searchHistory = [];
    }
}

// 全局智能搜索实例
const smartSearch = new SmartSearch();

// 高级搜索界面函数
function initializeSmartSearch() {
    // 创建搜索建议下拉框
    const searchInputs = document.querySelectorAll('input[type="text"][placeholder*="搜索"]');
    
    searchInputs.forEach(input => {
        const suggestionBox = createSuggestionBox(input);
        
        input.addEventListener('input', function() {
            const query = this.value.trim();
            if (query.length >= 2) {
                showSearchSuggestions(this, query, suggestionBox);
            } else {
                hideSuggestions(suggestionBox);
            }
        });
        
        input.addEventListener('blur', function() {
            // 延迟隐藏，允许点击建议
            setTimeout(() => hideSuggestions(suggestionBox), 200);
        });
    });
}

// 创建建议框
function createSuggestionBox(input) {
    const suggestionBox = document.createElement('div');
    suggestionBox.className = 'search-suggestions';
    suggestionBox.style.cssText = `
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ddd;
        border-top: none;
        border-radius: 0 0 4px 4px;
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    `;
    
    // 设置父容器为相对定位
    input.parentElement.style.position = 'relative';
    input.parentElement.appendChild(suggestionBox);
    
    return suggestionBox;
}

// 显示搜索建议
function showSearchSuggestions(input, query, suggestionBox) {
    const results = smartSearch.search(query, { includeApproximate: false });
    const suggestions = results.suggestions;
    
    if (suggestions.length === 0) {
        hideSuggestions(suggestionBox);
        return;
    }
    
    let html = '';
    suggestions.forEach(suggestion => {
        html += `
            <div class="suggestion-item" style="padding: 8px 12px; cursor: pointer; border-bottom: 1px solid #f0f0f0;" 
                 onmouseover="this.style.background='#f8f9fa'" 
                 onmouseout="this.style.background='white'"
                 onclick="selectSuggestion('${input.id}', '${suggestion}')">
                ${suggestion}
            </div>
        `;
    });
    
    suggestionBox.innerHTML = html;
    suggestionBox.style.display = 'block';
}

// 隐藏建议
function hideSuggestions(suggestionBox) {
    suggestionBox.style.display = 'none';
}

// 选择建议
function selectSuggestion(inputId, suggestion) {
    const input = document.getElementById(inputId);
    if (input) {
        input.value = suggestion;
        input.focus();
        // 触发搜索
        const event = new Event('input', { bubbles: true });
        input.dispatchEvent(event);
    }
}

// 执行智能产品搜索
function performSmartProductSearch(query, tolerance = NUMERIC_TOLERANCE) {
    const results = smartSearch.search(query, { 
        tolerance: tolerance,
        includeApproximate: true 
    });
    
    return results;
}

// 显示搜索结果
function displaySmartSearchResults(results, container) {
    if (!container) return;
    
    let html = '';
    
    // 精确匹配结果
    if (results.exact.length > 0) {
        html += '<div class="search-section"><h3 style="color: #be131b; margin-bottom: 15px;">精确匹配 (' + results.exact.length + ')</h3>';
        html += '<div class="products-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">';
        results.exact.forEach(product => {
            html += createProductCard(product);
        });
        html += '</div></div>';
    }
    
    // 近似匹配结果
    if (results.approximate.length > 0) {
        html += '<div class="search-section"><h3 style="color: #007bff; margin-bottom: 15px;">相近推荐 (' + results.approximate.length + ')</h3>';
        html += '<div class="products-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">';
        results.approximate.forEach(product => {
            html += createApproximateProductCard(product);
        });
        html += '</div></div>';
    }
    
    // 无结果提示
    if (results.exact.length === 0 && results.approximate.length === 0) {
        html = '<div style="text-align: center; padding: 40px; color: #666;">没有找到匹配的产品</div>';
    }
    
    container.innerHTML = html;
    
    // 添加点击事件
    container.querySelectorAll('.product-card').forEach(card => {
        card.addEventListener('click', function() {
            const productId = this.dataset.productId;
            showProductModal(productId);
        });
    });
}

// 创建近似匹配产品卡片
function createApproximateProductCard(product) {
    const productImages = [
        '16029111.png', '16029112.png', '16029113.png', '16029114.png',
        '16029115.png', '16029116.png', '16029117.png', '16029118.png',
        '16029137.png', '16029138.png', '16029139.png', '16029140.png'
    ];
    
    const imageIndex = parseInt(product.id) % productImages.length;
    const defaultImage = productImages[imageIndex];
    
    // 生成匹配详情
    let matchInfo = '';
    if (product.matchDetails && product.matchDetails.length > 0) {
        matchInfo = '<div style="background: #e3f2fd; padding: 8px; border-radius: 4px; margin-top: 10px; font-size: 12px;">';
        matchInfo += '<strong>相似度:</strong> ' + Math.round(product.relevanceScore * 10) + '/100<br>';
        product.matchDetails.slice(0, 2).forEach(detail => {
            matchInfo += `${detail.queryValue} ≈ ${detail.fieldValue} (差值: ${detail.difference})<br>`;
        });
        matchInfo += '</div>';
    }
    
    return `
        <div class="product-card approximate-match" data-product-id="${product.id}" style="border: 2px solid #007bff;">
            <div class="product-image">
                <img src="${defaultImage}" alt="${product.product_name}" style="width: 100%; height: 100%; object-fit: cover;">
                <div style="position: absolute; top: 5px; right: 5px; background: #007bff; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">相近</div>
            </div>
            <div class="product-info">
                <div class="product-title">
                    ${product.product_name}
                    <span class="product-id">${product.data_id}</span>
                </div>
                <div class="product-details">类别: ${product.product_category}</div>
                <div class="product-details">规格: ${product.specifications}</div>
                <div class="product-details">材质: ${product.material}</div>
                <div class="product-details">存货编码: ${product.stock_code}</div>
                ${matchInfo}
                ${getPermissionNotice()}
            </div>
        </div>
    `;
}
