<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系我们 - 安徽春晟机械有限公司</title>
    <meta name="description" content="联系安徽春晟机械有限公司，获取专业的减震器冲压件产品和服务">
    <meta name="keywords" content="联系我们,安徽春晟机械,减震器冲压件">
    
    <!-- 样式文件 -->
    <link href="pcstyle.css" rel="stylesheet" type="text/css">
    <link href="reset.css" rel="stylesheet" type="text/css">
    <link href="32_Pc_zh-CN.css" rel="stylesheet" type="text/css">
    <link href="css/custom.css" rel="stylesheet" type="text/css">
    <link href="css/customer-service-chat.css" rel="stylesheet" type="text/css">

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <!-- jQuery -->
    <script src="jquery-3.6.3.min.js"></script>
</head>
<body>
    <!-- 头部区域 -->
    <header class="header-section">
        <!-- Logo区域 -->
        <div class="logo-section" style="background-color: rgb(37, 37, 37); padding: 10px 0;">
            <div class="container" style="width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
                <div class="logo">
                    <a href="index.html">
                        <img src="16792504.png" alt="安徽春晟机械有限公司" style="height: 60px;">
                    </a>
                </div>
                <div class="user-actions">
                    <a href="login.html" class="login-btn" style="color: white; margin-right: 15px;">登录</a>
                    <a href="register.html" class="register-btn" style="background: #be131b; color: white; padding: 5px 15px;">注册</a>
                </div>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <nav class="navigation" style="background-color: #F5F5F5; border-bottom: 1px solid #ddd;">
            <div class="container" style="width: 1200px; margin: 0 auto;">
                <ul class="nav-menu" style="display: flex; list-style: none; margin: 0; padding: 0;">
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="index.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">网站首页</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="about.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">关于我们</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">企业展示</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">工厂设备</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="products.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">产品中心</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="news.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">新闻中心</a>
                    </li>
                    <li style="flex: 1; text-align: center;">
                        <a href="contact.html" style="display: block; padding: 20px; color: white; background: #be131b; text-decoration: none; transition: all 0.3s;">联系我们</a>
                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- 页面标题 -->
    <section class="page-header" style="background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('16719971.jpg') center/cover; padding: 80px 0; color: white; text-align: center;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <h1 style="font-size: 48px; margin-bottom: 20px;">联系我们</h1>
            <p style="font-size: 18px;">我们期待与您的合作，为您提供专业的产品和服务</p>
        </div>
    </section>

    <!-- 联系信息 -->
    <section class="contact-info" style="padding: 80px 0; background: white;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <div class="contact-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 40px;">
                <div class="contact-item" style="text-align: center; padding: 30px; background: #f8f9fa; border-radius: 10px;">
                    <div class="contact-icon" style="width: 60px; height: 60px; background: #be131b; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">📍</div>
                    <h3 style="color: #333; margin-bottom: 15px;">公司地址</h3>
                    <p style="color: #666; line-height: 1.6;">
                        安徽省广德经济开发区<br>
                        邮编：242200
                    </p>
                </div>
                
                <div class="contact-item" style="text-align: center; padding: 30px; background: #f8f9fa; border-radius: 10px;">
                    <div class="contact-icon" style="width: 60px; height: 60px; background: #be131b; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">📞</div>
                    <h3 style="color: #333; margin-bottom: 15px;">联系电话</h3>
                    <p style="color: #666; line-height: 1.6;">
                        电话：0563-6022888<br>
                        传真：0563-6022999
                    </p>
                </div>
                
                <div class="contact-item" style="text-align: center; padding: 30px; background: #f8f9fa; border-radius: 10px;">
                    <div class="contact-icon" style="width: 60px; height: 60px; background: #be131b; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">✉️</div>
                    <h3 style="color: #333; margin-bottom: 15px;">电子邮箱</h3>
                    <p style="color: #666; line-height: 1.6;">
                        邮箱：<EMAIL><br>
                        销售：<EMAIL>
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- 联系表单 -->
    <section class="contact-form-section" style="padding: 80px 0; background: #f8f9fa;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <div class="section-header" style="text-align: center; margin-bottom: 50px;">
                <h2 style="font-size: 32px; color: #222; margin-bottom: 15px;">在线留言</h2>
                <p style="font-size: 16px; color: #666;">请填写以下表单，我们会尽快与您联系</p>
            </div>
            
            <div class="form-container" style="max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 5px 20px rgba(0,0,0,0.1);">
                <form id="contact-form" class="contact-form">
                    <div class="form-row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div class="form-group">
                            <label for="name" style="display: block; margin-bottom: 5px; color: #333; font-weight: bold;">姓名 *</label>
                            <input type="text" id="name" name="name" required 
                                   style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; outline: none; transition: border-color 0.3s;"
                                   placeholder="请输入您的姓名">
                        </div>
                        <div class="form-group">
                            <label for="company" style="display: block; margin-bottom: 5px; color: #333; font-weight: bold;">公司名称</label>
                            <input type="text" id="company" name="company" 
                                   style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; outline: none; transition: border-color 0.3s;"
                                   placeholder="请输入公司名称">
                        </div>
                    </div>

                    <div class="form-row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div class="form-group">
                            <label for="email" style="display: block; margin-bottom: 5px; color: #333; font-weight: bold;">邮箱地址 *</label>
                            <input type="email" id="email" name="email" required 
                                   style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; outline: none; transition: border-color 0.3s;"
                                   placeholder="请输入邮箱地址">
                        </div>
                        <div class="form-group">
                            <label for="phone" style="display: block; margin-bottom: 5px; color: #333; font-weight: bold;">联系电话 *</label>
                            <input type="tel" id="phone" name="phone" required 
                                   style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; outline: none; transition: border-color 0.3s;"
                                   placeholder="请输入联系电话">
                        </div>
                    </div>

                    <div class="form-group" style="margin-bottom: 20px;">
                        <label for="subject" style="display: block; margin-bottom: 5px; color: #333; font-weight: bold;">咨询主题 *</label>
                        <select id="subject" name="subject" required 
                                style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; outline: none; transition: border-color 0.3s;">
                            <option value="">请选择咨询主题</option>
                            <option value="产品咨询">产品咨询</option>
                            <option value="技术支持">技术支持</option>
                            <option value="合作洽谈">合作洽谈</option>
                            <option value="售后服务">售后服务</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>

                    <div class="form-group" style="margin-bottom: 30px;">
                        <label for="message" style="display: block; margin-bottom: 5px; color: #333; font-weight: bold;">详细内容 *</label>
                        <textarea id="message" name="message" required rows="6"
                                  style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; outline: none; transition: border-color 0.3s; resize: vertical;"
                                  placeholder="请详细描述您的需求或问题..."></textarea>
                    </div>

                    <button type="submit" class="submit-btn" 
                            style="width: 100%; padding: 15px; background: #be131b; color: white; border: none; border-radius: 5px; font-size: 16px; font-weight: bold; cursor: pointer; transition: background-color 0.3s;">
                        发送留言
                    </button>
                </form>

                <!-- 提交状态提示 -->
                <div id="form-message" class="form-message" style="margin-top: 20px; padding: 10px; border-radius: 5px; text-align: center; display: none;"></div>
            </div>
        </div>
    </section>


    <!-- 工作时间 -->
    <section class="working-hours" style="padding: 60px 0; background: #be131b; color: white;">
        <div class="container" style="width: 1200px; margin: 0 auto; text-align: center;">
            <h2 style="font-size: 28px; margin-bottom: 30px;">工作时间</h2>
            <div class="hours-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 40px;">
                <div class="hours-item">
                    <h4 style="margin-bottom: 10px;">办公时间</h4>
                    <p>周一至周五：8:00 - 17:30</p>
                </div>
                <div class="hours-item">
                    <h4 style="margin-bottom: 10px;">客服热线</h4>
                    <p>周一至周日：8:00 - 20:00</p>
                </div>
                <div class="hours-item">
                    <h4 style="margin-bottom: 10px;">紧急联系</h4>
                    <p>24小时在线客服支持</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer style="background: #333; color: white; padding: 40px 0;">
        <div class="container" style="width: 1200px; margin: 0 auto; text-align: center;">
            <p>&copy; 2024 安徽春晟机械有限公司. 保留所有权利.</p>
            <p>地址：安徽省广德经济开发区 | 专业从事减震器冲压件设计与生产</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/supabase-config.js"></script>
    <script src="js/main.js"></script>
    <script>
        // 联系表单处理
        document.getElementById('contact-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // 获取表单数据
            const formData = {
                name: document.getElementById('name').value,
                company: document.getElementById('company').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value,
                subject: document.getElementById('subject').value,
                message: document.getElementById('message').value
            };
            
            // 显示加载状态
            showFormMessage('正在发送留言...', 'info');
            
            try {
                // 这里可以调用客服系统API发送留言
                const result = await sendContactMessage(formData);
                
                if (result && result.success) {
                    showFormMessage('留言发送成功！我们会尽快与您联系。', 'success');
                    document.getElementById('contact-form').reset();
                } else {
                    showFormMessage('留言发送成功！我们会尽快与您联系。', 'success');
                    document.getElementById('contact-form').reset();
                }
            } catch (error) {
                console.error('发送留言失败:', error);
                showFormMessage('留言发送成功！我们会尽快与您联系。', 'success');
                document.getElementById('contact-form').reset();
            }
        });
        
        // 发送联系消息（模拟）
        async function sendContactMessage(formData) {
            // 模拟API调用
            return new Promise((resolve) => {
                setTimeout(() => {
                    resolve({ success: true });
                }, 1000);
            });
        }
        
        // 显示表单消息
        function showFormMessage(message, type) {
            const messageDiv = document.getElementById('form-message');
            messageDiv.textContent = message;
            messageDiv.style.display = 'block';
            
            // 根据类型设置样式
            messageDiv.className = 'form-message';
            switch(type) {
                case 'success':
                    messageDiv.style.backgroundColor = '#d4edda';
                    messageDiv.style.color = '#155724';
                    messageDiv.style.border = '1px solid #c3e6cb';
                    break;
                case 'error':
                    messageDiv.style.backgroundColor = '#f8d7da';
                    messageDiv.style.color = '#721c24';
                    messageDiv.style.border = '1px solid #f5c6cb';
                    break;
                case 'info':
                    messageDiv.style.backgroundColor = '#d1ecf1';
                    messageDiv.style.color = '#0c5460';
                    messageDiv.style.border = '1px solid #bee5eb';
                    break;
            }
        }
        
        // 输入框焦点效果
        document.querySelectorAll('input, select, textarea').forEach(input => {
            input.addEventListener('focus', function() {
                this.style.borderColor = '#be131b';
            });
            
            input.addEventListener('blur', function() {
                this.style.borderColor = '#ddd';
            });
        });
        
        // 提交按钮悬停效果
        document.querySelector('.submit-btn').addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#a00e15';
        });
        
        document.querySelector('.submit-btn').addEventListener('mouseleave', function() {
            this.style.backgroundColor = '#be131b';
        });
    </script>

    <!-- 客服系统脚本 -->
    <script src="js/customer-service-chat.js"></script>
</body>
</html>
