/* 自定义样式 */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    line-height: 1.6;
}

/* 导航栏悬停效果 */
.nav-menu li a:hover {
    background-color: #be131b !important;
    color: white !important;
}

/* 轮播图样式 */
.slider-container {
    position: relative;
    overflow: hidden;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.slide.active {
    opacity: 1;
}

.slide-content {
    position: absolute;
    left: 100px;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    z-index: 2;
}

.slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
}

/* 产品卡片样式 */
.product-card {
    background: white;
    border: 1px solid #e0e0e0;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.product-image {
    width: 100%;
    height: 200px;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 14px;
}

.product-info {
    padding: 20px;
}

.product-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
}

.product-details {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.product-id {
    font-size: 12px;
    color: #999;
    float: right;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 40px;
}

.page-btn {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    color: #666;
    text-decoration: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.page-btn:hover {
    background: #e74c3c;
    color: white;
    border-color: #e74c3c;
}

.page-btn.active {
    background: #e74c3c;
    color: white;
    border-color: #e74c3c;
}

.page-numbers {
    display: flex;
    gap: 5px;
}

/* 用户权限提示 */
.permission-notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
    font-size: 12px;
}

.permission-notice.premium {
    background: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.permission-notice.special {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 20px;
    border-radius: 8px;
    width: 80%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.modal-title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.close {
    font-size: 28px;
    font-weight: bold;
    color: #aaa;
    cursor: pointer;
}

.close:hover {
    color: #000;
}

.modal-body {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.product-detail-image {
    width: 100%;
    height: 300px;
    background: #f0f0f0;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
}

.product-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.detail-item {
    margin-bottom: 15px;
}

.detail-label {
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.detail-value {
    color: #666;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 10px 20px;
    background: #be131b;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: background 0.3s ease;
}

.btn:hover {
    background: #a01117;
}

.btn-secondary {
    background: #6c757d;
}

.btn-secondary:hover {
    background: #545b62;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        width: 95% !important;
        padding: 0 10px;
    }
    
    .nav-menu {
        flex-direction: column !important;
    }
    
    .nav-menu li {
        border-right: none !important;
        border-bottom: 1px solid #ddd;
    }
    
    .slide-content {
        left: 20px !important;
        right: 20px;
    }
    
    .slide-content h1 {
        font-size: 28px !important;
    }
    
    .company-intro .container {
        flex-direction: column !important;
    }
    
    .intro-content {
        padding-right: 0 !important;
        margin-bottom: 30px;
    }
    
    .modal-body {
        grid-template-columns: 1fr !important;
    }
    
    .products-grid {
        grid-template-columns: 1fr !important;
    }
}

/* 加载动画 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #be131b;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 权限提示样式 */
.permission-notice {
    background: #fff3cd;
    color: #856404;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ffeaa7;
    margin: 10px 0;
    font-size: 14px;
    text-align: center;
}

.permission-notice.premium {
    background: #d1ecf1;
    color: #0c5460;
    border-color: #bee5eb;
}

.permission-notice.special {
    background: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

/* 下载按钮样式 */
.btn-download {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-download:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-download:active {
    transform: translateY(0);
}

.download-section {
    border-top: 1px solid #eee;
    padding-top: 15px;
    margin-top: 15px;
}

/* 通知动画 */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 产品详情区域样式 */
.detail-section {
    margin: 20px 0;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 8px;
    border-left: 4px solid #2196f3;
}

.detail-section h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.detail-item {
    display: flex;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.detail-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.detail-label {
    flex: 0 0 120px;
    font-weight: 500;
    color: #666;
}

.detail-value {
    flex: 1;
    color: #333;
}

/* 权限提示增强 */
.permission-notice strong {
    display: block;
    margin-bottom: 8px;
    font-size: 16px;
}

.permission-notice br + • {
    margin-left: 10px;
}

/* 相似产品推荐样式 */
.similar-products-section {
    margin-top: 20px;
}

.similar-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.similar-product-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.similar-product-item:hover {
    border-color: #2196f3;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
    transform: translateY(-2px);
}

.similar-product-image {
    width: 80px;
    height: 80px;
    margin: 0 auto 10px;
    border-radius: 6px;
    overflow: hidden;
    background: #f5f5f5;
}

.similar-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.similar-product-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
    font-size: 14px;
    line-height: 1.3;
}

.similar-product-spec {
    color: #666;
    font-size: 12px;
    margin-bottom: 5px;
}

.similar-product-id {
    color: #2196f3;
    font-size: 12px;
    font-weight: 500;
}

/* 智能搜索样式 */
.smart-search-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

/* 智能搜索输入框样式 - 修复文字显示问题 */
.smart-search-section input,
.smart-search-section select {
    border: 2px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.95) !important;
    color: #333 !important;
    backdrop-filter: blur(10px);
}

.smart-search-section input::placeholder {
    color: rgba(102, 102, 102, 0.8) !important;
}

.smart-search-section input:focus,
.smart-search-section select:focus {
    border-color: #be131b !important;
    background: rgba(255, 255, 255, 1) !important;
    color: #333 !important;
}

.search-options label {
    color: rgba(255, 255, 255, 0.9);
}

/* 表单样式增强 */
.form-group {
    position: relative;
}

.form-group input:focus + label,
.form-group select:focus + label,
.form-group textarea:focus + label {
    color: #be131b;
}

.form-group input:valid,
.form-group select:valid,
.form-group textarea:valid {
    border-color: #28a745;
}

.form-group input:invalid:not(:placeholder-shown),
.form-group select:invalid:not(:placeholder-shown),
.form-group textarea:invalid:not(:placeholder-shown) {
    border-color: #dc3545;
}

/* 页面过渡动画 */
.page-transition {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 卡片悬停效果增强 */
.permission-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.culture-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    transition: all 0.3s ease;
}

/* 时间线样式 */
.timeline-item:hover .timeline-content {
    transform: scale(1.02);
    transition: transform 0.3s ease;
}

.timeline-dot {
    box-shadow: 0 0 0 4px rgba(190, 19, 27, 0.2);
    transition: box-shadow 0.3s ease;
}

.timeline-item:hover .timeline-dot {
    box-shadow: 0 0 0 8px rgba(190, 19, 27, 0.3);
}

/* 导航栏活跃状态 */
.nav-menu li a.active {
    background-color: #be131b !important;
    color: white !important;
}

/* 搜索历史标签样式 */
.search-history-tag {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border: none;
    transition: all 0.3s ease;
}

.search-history-tag:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(245, 87, 108, 0.4);
}

/* 客服聊天按钮增强 */
.cs-chat-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }
    50% {
        box-shadow: 0 4px 20px rgba(102, 126, 234, 0.6);
    }
    100% {
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }
}

/* 产品图片懒加载效果 */
.product-image img {
    transition: opacity 0.3s ease;
}

.product-image img.loading {
    opacity: 0.5;
}

.product-image img.loaded {
    opacity: 1;
}

/* 错误状态样式 */
.error-message {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    padding: 15px;
    border-radius: 5px;
    margin: 20px 0;
    text-align: center;
}

.success-message {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    padding: 15px;
    border-radius: 5px;
    margin: 20px 0;
    text-align: center;
}

/* 工具提示样式 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #be131b;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a01117;
}

/* PDF预览样式 */
.pdf-preview-container {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
    margin-bottom: 15px;
}

.pdf-preview-container iframe {
    border: none;
    border-radius: 8px;
    width: 100%;
    height: 400px;
}

.pdf-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.pdf-actions .btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.pdf-actions .btn-primary {
    background: #007bff;
    color: white;
}

.pdf-actions .btn-primary:hover {
    background: #0056b3;
}

.pdf-actions .btn-download {
    background: #28a745;
    color: white;
}

.pdf-actions .btn-download:hover {
    background: #1e7e34;
}

.no-document-notice {
    padding: 20px;
    text-align: center;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px dashed #dee2e6;
    color: #666;
    font-style: italic;
}

/* 响应式PDF预览 */
@media (max-width: 768px) {
    .pdf-preview-container iframe {
        height: 300px;
    }

    .pdf-actions {
        flex-direction: column;
        gap: 8px;
    }

    .pdf-actions .btn {
        width: 100%;
        justify-content: center;
    }
}

/* 打印样式 */
@media print {
    .header-section,
    .navigation,
    .cs-chat-widget,
    footer {
        display: none !important;
    }

    .container {
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .product-card {
        break-inside: avoid;
        margin-bottom: 20px;
    }
}
