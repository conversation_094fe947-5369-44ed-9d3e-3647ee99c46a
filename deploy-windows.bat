@echo off
chcp 65001 >nul
echo ==========================================
echo   春晟机械网站部署到 222.79.104.168
echo ==========================================
echo.

set SERVER=222.79.104.168
set USER=root
set PASS=Mm.124578

echo 1. 检查必要工具...

:: 检查是否有pscp (PuTTY的scp工具)
where pscp >nul 2>&1
if %errorlevel% neq 0 (
    echo 未找到pscp工具，请安装PuTTY套件
    echo 下载地址: https://www.putty.org/
    echo 或者使用WSL/Git Bash运行Linux脚本
    pause
    exit /b 1
)

:: 检查是否有plink (PuTTY的ssh工具)
where plink >nul 2>&1
if %errorlevel% neq 0 (
    echo 未找到plink工具，请安装PuTTY套件
    pause
    exit /b 1
)

echo ✅ 工具检查完成

echo.
echo 2. 测试服务器连接...
echo y | plink -ssh %USER%@%SERVER% -pw %PASS% "echo 连接成功" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 无法连接到服务器，请检查IP和密码
    pause
    exit /b 1
)
echo ✅ 服务器连接正常

echo.
echo 3. 在服务器上安装Nginx...
echo y | plink -ssh %USER%@%SERVER% -pw %PASS% -m install-nginx.txt
echo ✅ Nginx安装完成

echo.
echo 4. 上传网站文件...
:: 创建临时文件列表，排除不需要的文件
dir /b /s *.html *.css *.js *.jpg *.png *.gif *.pdf *.svg > temp_files.txt
for /f "delims=" %%i in (temp_files.txt) do (
    set "file=%%i"
    set "file=!file:%cd%\=!"
    pscp -pw %PASS% "%%i" %USER%@%SERVER%:/var/www/chunsheng/
)
del temp_files.txt
echo ✅ 文件上传完成

echo.
echo 5. 配置Nginx...
echo y | plink -ssh %USER%@%SERVER% -pw %PASS% -m configure-nginx.txt
echo ✅ Nginx配置完成

echo.
echo 6. 设置权限和重启服务...
echo y | plink -ssh %USER%@%SERVER% -pw %PASS% -m setup-permissions.txt
echo ✅ 权限设置完成

echo.
echo ==========================================
echo 🎉 部署完成！
echo ==========================================
echo.
echo 访问地址：
echo   主页: http://222.79.104.168
echo   产品页面: http://222.79.104.168/products.html
echo   管理后台: http://222.79.104.168/admin/login.html
echo.
echo 管理员登录信息：
echo   用户名: admin
echo   密码: admin123
echo.
pause
