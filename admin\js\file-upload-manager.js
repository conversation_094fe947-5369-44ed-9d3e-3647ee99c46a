// 文件上传管理器
class FileUploadManager {
    constructor() {
        this.batchImages = [];
        this.batchPdfs = [];
        this.uploadProgress = 0;
        this.setupDragAndDrop();
    }

    // 设置拖拽上传
    setupDragAndDrop() {
        const imageArea = document.getElementById('images-upload-area');
        const pdfArea = document.getElementById('pdfs-upload-area');

        if (imageArea) {
            this.setupDropZone(imageArea, 'image');
        }
        if (pdfArea) {
            this.setupDropZone(pdfArea, 'pdf');
        }
    }

    setupDropZone(element, type) {
        element.addEventListener('dragover', (e) => {
            e.preventDefault();
            element.style.borderColor = '#be131b';
            element.style.backgroundColor = '#f8f9fa';
        });

        element.addEventListener('dragleave', (e) => {
            e.preventDefault();
            element.style.borderColor = '#ddd';
            element.style.backgroundColor = 'transparent';
        });

        element.addEventListener('drop', (e) => {
            e.preventDefault();
            element.style.borderColor = '#ddd';
            element.style.backgroundColor = 'transparent';

            const files = Array.from(e.dataTransfer.files);
            if (type === 'image') {
                this.handleBatchImages(files);
            } else if (type === 'pdf') {
                this.handleBatchPdfs(files);
            }
        });
    }

    // 处理批量图片
    handleBatchImages(files) {
        const validImages = files.filter(file => {
            const isImage = file.type.startsWith('image/');
            const isValidSize = file.size <= 5 * 1024 * 1024; // 5MB
            
            if (!isImage) {
                showError(`文件 ${file.name} 不是有效的图片格式`);
                return false;
            }
            if (!isValidSize) {
                showError(`文件 ${file.name} 超过5MB大小限制`);
                return false;
            }
            return true;
        });

        this.batchImages = validImages;
        this.displayImagePreview();
    }

    // 处理批量PDF
    handleBatchPdfs(files) {
        const validPdfs = files.filter(file => {
            const isPdf = file.type === 'application/pdf';
            const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB
            
            if (!isPdf) {
                showError(`文件 ${file.name} 不是有效的PDF格式`);
                return false;
            }
            if (!isValidSize) {
                showError(`文件 ${file.name} 超过10MB大小限制`);
                return false;
            }
            return true;
        });

        this.batchPdfs = validPdfs;
        this.displayPdfPreview();
    }

    // 显示图片预览
    displayImagePreview() {
        const previewContainer = document.getElementById('images-preview');
        const previewGrid = document.getElementById('images-preview-grid');
        
        if (this.batchImages.length === 0) {
            previewContainer.style.display = 'none';
            return;
        }

        previewGrid.innerHTML = '';
        
        this.batchImages.forEach((file, index) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const imageCard = document.createElement('div');
                imageCard.style.cssText = `
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    padding: 10px;
                    text-align: center;
                    background: white;
                `;
                
                const dataId = this.extractDataIdFromFilename(file.name);
                
                imageCard.innerHTML = `
                    <img src="${e.target.result}" alt="${file.name}" style="width: 100%; height: 100px; object-fit: cover; border-radius: 4px; margin-bottom: 8px;">
                    <div style="font-size: 12px; font-weight: bold; margin-bottom: 4px;">${file.name}</div>
                    <div style="font-size: 11px; color: #666; margin-bottom: 8px;">数据ID: ${dataId || '未识别'}</div>
                    <button class="btn btn-danger btn-sm" onclick="fileUploadManager.removeImage(${index})" style="font-size: 11px; padding: 2px 6px;">移除</button>
                `;
                
                previewGrid.appendChild(imageCard);
            };
            reader.readAsDataURL(file);
        });

        previewContainer.style.display = 'block';
    }

    // 显示PDF预览
    displayPdfPreview() {
        const previewContainer = document.getElementById('pdfs-preview');
        const previewList = document.getElementById('pdfs-preview-list');
        
        if (this.batchPdfs.length === 0) {
            previewContainer.style.display = 'none';
            return;
        }

        previewList.innerHTML = '';
        
        this.batchPdfs.forEach((file, index) => {
            const dataId = this.extractDataIdFromFilename(file.name);
            
            const pdfCard = document.createElement('div');
            pdfCard.style.cssText = `
                display: flex;
                align-items: center;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 8px;
                margin-bottom: 10px;
                background: white;
            `;
            
            pdfCard.innerHTML = `
                <div style="font-size: 24px; margin-right: 15px;">📄</div>
                <div style="flex: 1;">
                    <div style="font-weight: bold; margin-bottom: 4px;">${file.name}</div>
                    <div style="font-size: 12px; color: #666;">
                        大小: ${(file.size / 1024 / 1024).toFixed(2)} MB | 数据ID: ${dataId || '未识别'}
                    </div>
                </div>
                <button class="btn btn-danger btn-sm" onclick="fileUploadManager.removePdf(${index})">移除</button>
            `;
            
            previewList.appendChild(pdfCard);
        });

        previewContainer.style.display = 'block';
    }

    // 从文件名提取数据ID
    extractDataIdFromFilename(filename) {
        // 匹配 A0001, B0002 等格式
        const match = filename.match(/([A-Z]\d{4})/i);
        return match ? match[1].toUpperCase() : null;
    }

    // 移除图片
    removeImage(index) {
        this.batchImages.splice(index, 1);
        this.displayImagePreview();
    }

    // 移除PDF
    removePdf(index) {
        this.batchPdfs.splice(index, 1);
        this.displayPdfPreview();
    }

    // 清除图片预览
    clearImagePreview() {
        this.batchImages = [];
        document.getElementById('images-preview').style.display = 'none';
        document.getElementById('batch-images-input').value = '';
    }

    // 清除PDF预览
    clearPdfPreview() {
        this.batchPdfs = [];
        document.getElementById('pdfs-preview').style.display = 'none';
        document.getElementById('batch-pdfs-input').value = '';
    }

    // 上传批量图片
    async uploadBatchImages() {
        if (this.batchImages.length === 0) {
            showError('请先选择图片文件');
            return;
        }

        this.showUploadProgress();
        const results = [];
        
        for (let i = 0; i < this.batchImages.length; i++) {
            const file = this.batchImages[i];
            const dataId = this.extractDataIdFromFilename(file.name);
            
            try {
                this.updateProgress((i / this.batchImages.length) * 100, `正在上传: ${file.name}`);
                
                const result = await this.uploadImageFile(file, dataId);
                results.push({ file: file.name, dataId, success: true, url: result.url });
                
                // 更新产品记录
                if (dataId && result.url) {
                    await this.updateProductImage(dataId, result.url);
                }
                
            } catch (error) {
                console.error('上传失败:', error);
                results.push({ file: file.name, dataId, success: false, error: error.message });
            }
        }
        
        this.updateProgress(100, '上传完成');
        this.showUploadResults(results, 'images');
    }

    // 上传批量PDF
    async uploadBatchPdfs() {
        if (this.batchPdfs.length === 0) {
            showError('请先选择PDF文件');
            return;
        }

        this.showUploadProgress();
        const results = [];
        
        for (let i = 0; i < this.batchPdfs.length; i++) {
            const file = this.batchPdfs[i];
            const dataId = this.extractDataIdFromFilename(file.name);
            
            try {
                this.updateProgress((i / this.batchPdfs.length) * 100, `正在上传: ${file.name}`);
                
                const result = await this.uploadPdfFile(file, dataId);
                results.push({ file: file.name, dataId, success: true, url: result.url });
                
                // 更新产品记录
                if (dataId && result.url) {
                    await this.updateProductPdf(dataId, result.url);
                }
                
            } catch (error) {
                console.error('上传失败:', error);
                results.push({ file: file.name, dataId, success: false, error: error.message });
            }
        }
        
        this.updateProgress(100, '上传完成');
        this.showUploadResults(results, 'pdfs');
    }

    // 上传单个图片文件
    async uploadImageFile(file, dataId) {
        // 如果文件名已经是正确格式（如A0014.jpg），直接使用
        let fileName;
        const fileExt = file.name.split('.').pop();
        if (file.name.match(/^[A-Z]\d{4}\.(jpg|jpeg|png|gif|bmp)$/i)) {
            fileName = file.name;
        } else {
            fileName = dataId ? `${dataId}.${fileExt}` : file.name;
        }

        // 清理文件名，确保没有中文字符
        fileName = this.sanitizeFileName(fileName);
        const filePath = `images/${fileName}`;

        const { data, error } = await supabase.storage
            .from('product-images')
            .upload(filePath, file, {
                cacheControl: '3600',
                upsert: true
            });

        if (error) throw error;

        const { data: urlData } = supabase.storage
            .from('product-images')
            .getPublicUrl(filePath);

        return { url: urlData.publicUrl, path: filePath };
    }

    // 清理文件名，移除中文字符和特殊字符
    sanitizeFileName(fileName) {
        // 中文到英文的映射
        const chineseToEnglish = {
            '技术文档': 'technical_doc',
            '说明书': 'manual',
            '产品手册': 'product_manual',
            '工艺文档': 'process_doc',
            '检验标准': 'inspection_standard',
            '用户指南': 'user_guide',
            '安装说明': 'installation_guide'
        };

        // 替换中文词汇
        let cleanName = fileName;
        for (const [chinese, english] of Object.entries(chineseToEnglish)) {
            cleanName = cleanName.replace(chinese, english);
        }

        // 移除其他中文字符和特殊字符，只保留字母、数字、下划线、连字符和点
        cleanName = cleanName.replace(/[^\w\-_.]/g, '_');

        // 移除多余的下划线
        cleanName = cleanName.replace(/_+/g, '_');

        return cleanName;
    }

    // 上传单个PDF文件
    async uploadPdfFile(file, dataId) {
        // 如果文件名已经包含数据ID，直接使用原文件名
        let fileName;
        if (file.name.startsWith(dataId + '_')) {
            fileName = file.name;
        } else {
            fileName = dataId ? `${dataId}_${file.name}` : file.name;
        }

        // 清理文件名
        fileName = this.sanitizeFileName(fileName);
        const filePath = `pdfs/${fileName}`;

        const { data, error } = await supabase.storage
            .from('product-pdfs')
            .upload(filePath, file, {
                cacheControl: '3600',
                upsert: true
            });

        if (error) throw error;

        const { data: urlData } = supabase.storage
            .from('product-pdfs')
            .getPublicUrl(filePath);

        return { url: urlData.publicUrl, path: filePath };
    }

    // 更新产品图片
    async updateProductImage(dataId, imageUrl) {
        const { error } = await supabase
            .from('products')
            .update({ product_image: imageUrl })
            .eq('data_id', dataId);
            
        if (error) throw error;
    }

    // 更新产品PDF
    async updateProductPdf(dataId, pdfUrl) {
        const { error } = await supabase
            .from('products')
            .update({ attachment_path: pdfUrl })
            .eq('data_id', dataId);

        if (error) throw error;
    }

    // 显示上传进度
    showUploadProgress() {
        document.getElementById('upload-progress').style.display = 'block';
        document.getElementById('upload-results').style.display = 'none';
    }

    // 更新进度
    updateProgress(percent, message) {
        document.getElementById('progress-bar').style.width = `${percent}%`;
        document.getElementById('progress-text').textContent = `${Math.round(percent)}%`;
        document.getElementById('progress-details').textContent = message;
    }

    // 显示上传结果
    showUploadResults(results, type) {
        document.getElementById('upload-progress').style.display = 'none';

        const resultsContainer = document.getElementById('upload-results-content');
        const successCount = results.filter(r => r.success).length;
        const failCount = results.length - successCount;

        let html = `
            <div style="margin-bottom: 20px;">
                <div style="display: flex; gap: 20px; margin-bottom: 15px;">
                    <div style="background: #d4edda; color: #155724; padding: 10px 15px; border-radius: 8px; flex: 1; text-align: center;">
                        ✅ 成功: ${successCount}
                    </div>
                    <div style="background: #f8d7da; color: #721c24; padding: 10px 15px; border-radius: 8px; flex: 1; text-align: center;">
                        ❌ 失败: ${failCount}
                    </div>
                </div>
            </div>
        `;

        if (results.length > 0) {
            html += '<div style="max-height: 300px; overflow-y: auto;">';
            results.forEach(result => {
                const status = result.success ? '✅' : '❌';
                const statusColor = result.success ? '#28a745' : '#dc3545';

                html += `
                    <div style="display: flex; align-items: center; padding: 8px; border-bottom: 1px solid #eee;">
                        <span style="color: ${statusColor}; margin-right: 10px;">${status}</span>
                        <div style="flex: 1;">
                            <div style="font-weight: bold;">${result.file}</div>
                            <div style="font-size: 12px; color: #666;">
                                数据ID: ${result.dataId || '未识别'}
                                ${result.success ? '' : ` | 错误: ${result.error}`}
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
        }

        resultsContainer.innerHTML = html;
        document.getElementById('upload-results').style.display = 'block';
    }
}

// 全局实例
let fileUploadManager;

// 初始化文件上传管理器
document.addEventListener('DOMContentLoaded', function() {
    fileUploadManager = new FileUploadManager();
});

// 全局函数
function handleBatchImageUpload(input) {
    const files = Array.from(input.files);
    fileUploadManager.handleBatchImages(files);
}

function handleBatchPdfUpload(input) {
    const files = Array.from(input.files);
    fileUploadManager.handleBatchPdfs(files);
}

function clearImagePreview() {
    fileUploadManager.clearImagePreview();
}

function clearPdfPreview() {
    fileUploadManager.clearPdfPreview();
}

function uploadBatchImages() {
    fileUploadManager.uploadBatchImages();
}

function uploadBatchPdfs() {
    fileUploadManager.uploadBatchPdfs();
}

// 调试：确认文件已加载
console.log('file-upload-manager.js 已加载');
