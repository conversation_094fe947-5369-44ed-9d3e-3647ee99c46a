<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新闻中心 - 安徽春晟机械有限公司</title>
    <meta name="description" content="安徽春晟机械有限公司新闻中心，了解公司最新动态、行业资讯和技术发展">
    <meta name="keywords" content="春晟机械,新闻动态,行业资讯,技术发展,减震器冲压件">
    
    <!-- 样式文件 -->
    <link href="pcstyle.css" rel="stylesheet" type="text/css">
    <link href="reset.css" rel="stylesheet" type="text/css">
    <link href="32_Pc_zh-CN.css" rel="stylesheet" type="text/css">
    <link href="css/custom.css" rel="stylesheet" type="text/css">
    <link href="css/customer-service-chat.css" rel="stylesheet" type="text/css">

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <!-- jQuery -->
    <script src="jquery-3.6.3.min.js"></script>

    <style>
        .news-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            margin-bottom: 30px;
        }

        .news-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .news-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .news-content {
            padding: 25px;
        }

        .news-date {
            color: #be131b;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .news-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .news-summary {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .news-tags {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
        }

        .news-tag {
            background: #f8f9fa;
            color: #666;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            border: 1px solid #e9ecef;
        }

        .read-more {
            color: #be131b;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .read-more:hover {
            color: #d32f2f;
        }

        .news-filter {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .filter-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 8px 20px;
            border: 2px solid #e9ecef;
            background: white;
            color: #666;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .filter-btn.active,
        .filter-btn:hover {
            background: #be131b;
            color: white;
            border-color: #be131b;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .news-card {
            animation: fadeInUp 0.6s ease-out;
        }

        .news-card:nth-child(1) { animation-delay: 0.1s; }
        .news-card:nth-child(2) { animation-delay: 0.2s; }
        .news-card:nth-child(3) { animation-delay: 0.3s; }
        .news-card:nth-child(4) { animation-delay: 0.4s; }
        .news-card:nth-child(5) { animation-delay: 0.5s; }
        .news-card:nth-child(6) { animation-delay: 0.6s; }
    </style>
</head>
<body>
    <!-- 头部区域 -->
    <header class="header-section">
        <!-- Logo区域 -->
        <div class="logo-section" style="background-color: rgb(37, 37, 37); padding: 10px 0;">
            <div class="container" style="width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
                <div class="logo">
                    <img src="16792504.png" alt="安徽春晟机械有限公司" style="height: 60px;">
                </div>
                <div class="user-actions">
                    <a href="login.html" class="login-btn" style="color: white; margin-right: 15px;">登录</a>
                    <a href="register.html" class="register-btn" style="background: #be131b; color: white; padding: 5px 15px;">注册</a>
                </div>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <nav class="navigation" style="background-color: #F5F5F5; border-bottom: 1px solid #ddd;">
            <div class="container" style="width: 1200px; margin: 0 auto;">
                <ul class="nav-menu" style="display: flex; list-style: none; margin: 0; padding: 0;">
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="index.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">网站首页</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="about.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">关于我们</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">企业展示</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">工厂设备</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="products.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">产品中心</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="news.html" style="display: block; padding: 20px; color: white; background: #be131b; text-decoration: none; transition: all 0.3s;">新闻中心</a>
                    </li>
                    <li style="flex: 1; text-align: center;">
                        <a href="contact.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">联系我们</a>
                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- 页面标题 -->
    <section class="page-header" style="background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('16719971.jpg') center/cover; padding: 80px 0; color: white; text-align: center;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <h1 style="font-size: 48px; margin-bottom: 20px;">新闻中心</h1>
            <p style="font-size: 18px;">了解春晟机械最新动态与行业资讯</p>
        </div>
    </section>

    <!-- 新闻内容 -->
    <section class="news-section" style="padding: 60px 0; background: #f8f8f8;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <!-- 新闻筛选 -->
            <div class="news-filter">
                <h3 style="margin-bottom: 15px; color: #333;">新闻分类</h3>
                <div class="filter-buttons">
                    <button class="filter-btn active" data-category="all">全部新闻</button>
                    <button class="filter-btn" data-category="company">公司动态</button>
                    <button class="filter-btn" data-category="industry">行业资讯</button>
                    <button class="filter-btn" data-category="technology">技术发展</button>
                    <button class="filter-btn" data-category="product">产品更新</button>
                </div>
            </div>

            <!-- 新闻列表 -->
            <div id="news-grid" class="news-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 30px;">
                <!-- 新闻将通过JavaScript动态加载 -->
                <div class="loading" style="grid-column: 1 / -1; text-align: center; padding: 40px;">
                    <div style="display: inline-block; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #be131b; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                    <p style="margin-top: 15px; color: #666;">正在加载新闻...</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer style="background: #333; color: white; padding: 40px 0;">
        <div class="container" style="width: 1200px; margin: 0 auto; text-align: center;">
            <p>&copy; 2024 安徽春晟机械有限公司. 保留所有权利.</p>
            <p>地址：安徽省广德经济开发区 | 专业从事减震器冲压件设计与生产</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/supabase-config.js"></script>
    <script src="js/main.js"></script>
    <script>
        // 模拟新闻数据
        const newsData = [
            {
                id: 1,
                title: "春晟机械荣获\"优秀供应商\"称号",
                summary: "近日，安徽春晟机械有限公司凭借优质的产品质量和完善的服务体系，荣获多家知名汽车制造商颁发的\"优秀供应商\"称号。",
                content: "这一荣誉的获得，充分体现了春晟机械在减震器冲压件领域的专业实力和市场认可度...",
                date: "2024-06-28",
                category: "company",
                tags: ["荣誉", "供应商", "汽车制造"],
                image: "16719971.jpg"
            },
            {
                id: 2,
                title: "新型减震器冲压件生产线正式投产",
                summary: "春晟机械投资引进的全新自动化生产线正式投入使用，大幅提升了生产效率和产品质量稳定性。",
                content: "该生产线采用先进的数控技术和智能化管理系统，能够实现24小时连续生产...",
                date: "2024-06-25",
                category: "product",
                tags: ["生产线", "自动化", "技术升级"],
                image: "16719962.jpg"
            },
            {
                id: 3,
                title: "汽车零部件行业发展趋势分析",
                summary: "随着新能源汽车的快速发展，汽车零部件行业正面临新的机遇与挑战，轻量化、智能化成为发展主流。",
                content: "行业专家分析认为，未来汽车零部件将更加注重环保性能和智能化集成...",
                date: "2024-06-22",
                category: "industry",
                tags: ["行业分析", "新能源", "轻量化"],
                image: "16719937.jpg"
            }
        ];

        // 渲染新闻列表
        function renderNews(newsArray = newsData) {
            const newsGrid = document.getElementById('news-grid');
            
            if (newsArray.length === 0) {
                newsGrid.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 40px;">
                        <img src="no-data.png" alt="暂无数据" style="width: 100px; opacity: 0.5;">
                        <p style="color: #999; margin-top: 15px;">暂无相关新闻</p>
                    </div>
                `;
                return;
            }

            const newsHTML = newsArray.map(news => `
                <article class="news-card">
                    <img src="${news.image}" alt="${news.title}" class="news-image">
                    <div class="news-content">
                        <div class="news-date">${news.date}</div>
                        <h3 class="news-title">${news.title}</h3>
                        <div class="news-tags">
                            ${news.tags.map(tag => `<span class="news-tag">${tag}</span>`).join('')}
                        </div>
                        <p class="news-summary">${news.summary}</p>
                        <a href="news-detail.html?id=${news.id}" class="read-more">阅读全文 →</a>
                    </div>
                </article>
            `).join('');

            newsGrid.innerHTML = newsHTML;
        }

        // 筛选新闻
        function filterNews(category) {
            const filteredNews = category === 'all' ? newsData : newsData.filter(news => news.category === category);
            renderNews(filteredNews);
            
            // 更新按钮状态
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-category="${category}"]`).classList.add('active');
        }



        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 渲染新闻
            renderNews();
            
            // 绑定筛选按钮事件
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const category = this.getAttribute('data-category');
                    filterNews(category);
                });
            });
        });
    </script>

    <!-- 客服系统脚本 -->
    <script src="js/customer-service-chat.js"></script>
</body>
</html>
