<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 管理后台</title>
    
    <!-- 样式文件 -->
    <link href="../css/custom.css" rel="stylesheet" type="text/css">
    
    <style>
        body {
            margin: 0;
            background: #f5f5f5;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        .admin-layout {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            flex-shrink: 0;
        }
        
        .sidebar-header {
            padding: 20px;
            background: #be131b;
            text-align: center;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .sidebar-menu li {
            border-bottom: 1px solid #34495e;
        }
        
        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: white;
            text-decoration: none;
            transition: background 0.3s ease;
        }
        
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: #34495e;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .top-bar {
            background: white;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .content-area {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }
        
        .toolbar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .search-filters {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-filters input,
        .search-filters select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            transition: background 0.3s ease;
        }
        
        .btn-primary {
            background: #be131b;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .users-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        
        .user-row:hover {
            background: #f8f9fa;
        }
        
        .user-type-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .user-type-normal {
            background: #e9ecef;
            color: #495057;
        }
        
        .user-type-premium {
            background: #cce5ff;
            color: #0066cc;
        }
        
        .user-type-special {
            background: #d4edda;
            color: #155724;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .actions {
            display: flex;
            gap: 5px;
        }
        
        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .close {
            font-size: 28px;
            font-weight: bold;
            color: #aaa;
            cursor: pointer;
        }
        
        .close:hover {
            color: #000;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #be131b;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stats-number {
            font-size: 32px;
            font-weight: bold;
            color: #be131b;
            margin-bottom: 5px;
        }
        
        .stats-label {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>管理后台</h2>
            </div>
            
            <ul class="sidebar-menu">
                <li><a href="products.html">📦 产品管理</a></li>
                <li><a href="users.html" class="active">👥 用户管理</a></li>
                <li><a href="car-models.html">🚗 车型管理</a></li>
                <li><a href="customer-service.html">💬 客服管理</a></li>
                <li><a href="#" onclick="logout()" style="color: #ff6b6b;">🚪 退出</a></li>
            </ul>
        </div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部栏 -->
            <div class="top-bar">
                <h1 class="page-title">用户管理</h1>
                <div class="user-info">
                    <span>欢迎，<span id="admin-username">管理员</span></span>
                    <button class="btn btn-danger" onclick="logout()">退出登录</button>
                </div>
            </div>
            
            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 统计卡片 -->
                <div class="stats-cards">
                    <div class="stats-card">
                        <div class="stats-number" id="total-users">0</div>
                        <div class="stats-label">总用户数</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-number" id="premium-users">0</div>
                        <div class="stats-label">高级用户</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-number" id="privileged-users">0</div>
                        <div class="stats-label">特许用户</div>
                    </div>
                </div>
                
                <!-- 工具栏 -->
                <div class="toolbar">
                    <div class="search-filters">
                        <input type="text" id="search-input" placeholder="搜索用户名、邮箱、公司...">
                        <select id="user-type-filter">
                            <option value="">所有用户类型</option>
                            <option value="premium">高级用户</option>
                            <option value="privileged">特许用户</option>
                        </select>
                        <select id="status-filter">
                            <option value="">所有状态</option>
                            <option value="true">活跃</option>
                            <option value="false">禁用</option>
                        </select>
                        <button class="btn btn-secondary" onclick="searchUsers()">搜索</button>
                        <button class="btn btn-secondary" onclick="resetSearch()">重置</button>
                    </div>
                    
                    <div class="actions">
                        <button class="btn btn-primary" onclick="showAddUserModal()">➕ 添加用户</button>
                        <button class="btn btn-secondary" onclick="exportUsers()">📤 导出用户</button>
                    </div>
                </div>
                
                <!-- 用户表格 -->
                <div class="users-table">
                    <div class="table-header">
                        <h3>用户列表</h3>
                        <div>
                            <span id="users-count">0</span> 个用户
                        </div>
                    </div>
                    
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>邮箱</th>
                                    <th>用户类型</th>
                                    <th>公司名称</th>
                                    <th>手机号</th>
                                    <th>状态</th>
                                    <th>注册时间</th>
                                    <th>最近访问</th>
                                    <th>访问次数</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="users-tbody">
                                <tr>
                                    <td colspan="10" class="loading">
                                        <div class="spinner"></div>
                                        加载中...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script>
        // 初始化Supabase客户端（管理后台专用）
        const supabase = window.supabase.createClient(
            'https://snckktsqwrbfwtjlvcfr.supabase.co',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644'
        );
        console.log('🔧 [ADMIN] Supabase客户端初始化完成');
    </script>
    <script src="js/admin-common.js"></script>
    <script src="js/users-admin.js"></script>
</body>
</html>
