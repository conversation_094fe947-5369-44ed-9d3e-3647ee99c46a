<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车型管理 - 管理后台</title>

    <!-- 样式文件 -->
    <link href="../css/custom.css" rel="stylesheet" type="text/css">

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>

    <style>
        body {
            margin: 0;
            background: #f5f5f5;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }

        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            flex-shrink: 0;
        }

        .sidebar-header {
            padding: 20px;
            background: #be131b;
            text-align: center;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            border-bottom: 1px solid #34495e;
        }

        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: white;
            text-decoration: none;
            transition: background 0.3s ease;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: #34495e;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .top-bar {
            background: white;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-area {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .toolbar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            transition: background 0.3s ease;
        }

        .btn-primary {
            background: #be131b;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .car-models-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-container {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }

        .car-model-row:hover {
            background: #f8f9fa;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #be131b;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stats-number {
            font-size: 32px;
            font-weight: bold;
            color: #be131b;
            margin-bottom: 5px;
        }

        .stats-label {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>管理后台</h2>
            </div>

            <ul class="sidebar-menu">
                <li><a href="products.html">📦 产品管理</a></li>
                <li><a href="users.html">👥 用户管理</a></li>
                <li><a href="car-models.html" class="active">🚗 车型管理</a></li>
                <li><a href="customer-service.html">💬 客服管理</a></li>
                <li><a href="#" onclick="logout()" style="color: #ff6b6b;">🚪 退出</a></li>
            </ul>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部栏 -->
            <div class="top-bar">
                <h1 class="page-title">车型管理</h1>
                <div class="user-info">
                    <span>欢迎，<span id="admin-username">管理员</span></span>
                    <button class="btn btn-danger" onclick="logout()">退出登录</button>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 统计卡片 -->
                <div class="stats-cards">
                    <div class="stats-card">
                        <div class="stats-number" id="total-car-models">0</div>
                        <div class="stats-label">总车型数</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-number" id="total-brands">0</div>
                        <div class="stats-label">品牌数量</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-number" id="linked-models">0</div>
                        <div class="stats-label">已关联产品</div>
                    </div>
                </div>

                <!-- 工具栏 -->
                <div class="toolbar">
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <select id="brand-filter" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; min-width: 120px;">
                            <option value="">选择品牌</option>
                        </select>
                        <select id="model-filter" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; min-width: 120px;" disabled>
                            <option value="">选择车型</option>
                        </select>
                        <input type="text" id="product-id-filter" placeholder="产品ID..." style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; width: 120px;">
                        <button class="btn btn-secondary" onclick="searchCarModels()">搜索</button>
                        <button class="btn btn-secondary" onclick="resetSearch()">重置</button>
                    </div>

                    <div>
                        <button class="btn btn-primary" onclick="showAddCarModelModal()">➕ 添加车型</button>
                        <button class="btn btn-success" onclick="showImportModal()">📥 批量导入</button>
                        <button class="btn btn-info" onclick="downloadTemplate()">📋 下载模板</button>
                        <button class="btn btn-secondary" onclick="exportCarModels()">📤 导出车型</button>
                    </div>
                </div>

                <!-- 车型表格 -->
                <div class="car-models-table">
                    <div class="table-header">
                        <h3>车型列表</h3>
                        <div>
                            <span id="car-models-count">0</span> 个车型
                        </div>
                    </div>

                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>品牌</th>
                                    <th>车型</th>
                                    <th>关联产品数</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="car-models-tbody">
                                <tr>
                                    <td colspan="7" class="loading">
                                        <div class="spinner"></div>
                                        加载中...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑车型模态框 -->
    <div id="carModelModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 8px; width: 500px; max-width: 90%;">
            <h3 id="modalTitle">添加车型</h3>
            <form id="carModelForm">
                <input type="hidden" id="carModelId">

                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">品牌 *</label>
                    <input type="text" id="brand" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">车型 *</label>
                    <input type="text" id="model" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>

                <div style="margin-bottom: 20px;">
                <!-- 年份范围与描述字段已移除 -->

                <div style="text-align: right;">
                    <button type="button" class="btn btn-secondary" onclick="closeCarModelModal()" style="margin-right: 10px;">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveCarModel()">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 批量导入模态框 -->
    <div id="importModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 8px; width: 600px; max-width: 90%;">
            <h3>批量导入车型</h3>

            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">选择Excel文件</label>
                <input type="file" id="importFile" accept=".xlsx,.xls" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <div style="font-size: 12px; color: #666; margin-top: 5px;">
                    支持.xlsx和.xls格式，请使用提供的模板格式
                </div>
            </div>

            <div style="margin-bottom: 20px;">
                <h4 style="margin-bottom: 10px;">导入说明：</h4>
                <ul style="font-size: 14px; color: #666; margin: 0; padding-left: 20px;">
                    <li>Excel文件应包含：品牌、车型 两列；可选列：关联产品数据ID(逗号分隔)</li>
                    <li>品牌和车型为必填项；关联产品ID可留空或用英文逗号分隔多个ID，例如：1,2,5</li>
                    <li>建议先下载模板，按照格式填写数据</li>
                </ul>
            </div>

            <div id="importProgress" style="display: none; margin-bottom: 20px;">
                <div style="background: #f0f0f0; border-radius: 4px; overflow: hidden; margin-bottom: 10px;">
                    <div id="progressBar" style="background: #28a745; height: 20px; width: 0%; transition: width 0.3s;"></div>
                </div>
                <div id="importStatus" style="font-size: 14px; color: #666;"></div>
            </div>

            <div style="text-align: right;">
                <button type="button" class="btn btn-secondary" onclick="closeImportModal()" style="margin-right: 10px;">取消</button>
                <button type="button" class="btn btn-primary" onclick="importCarModels()">开始导入</button>
            </div>
        </div>
    </div>

    <!-- 车型关联产品模态框 -->
    <div id="carModelProductsModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 8px; width: 800px; max-width: 95%; max-height: 90%; overflow-y: auto;">
            <h3 id="carModelProductsTitle">车型关联产品</h3>

            <!-- 车型信息 -->
            <div id="carModelInfo" style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
                <!-- 车型信息将在这里显示 -->
            </div>

            <!-- 添加产品关联 -->
            <div style="margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 6px;">
                <h4 style="margin-bottom: 15px;">添加产品关联</h4>
                <div style="display: flex; gap: 10px; align-items: end;">
                    <div style="flex: 1;">
                        <label style="display: block; margin-bottom: 5px; font-size: 12px; color: #666;">搜索/选择产品：</label>
                        <input type="text" id="productSearch" placeholder="输入数据ID或产品名称快速筛选" oninput="filterProductOptions()" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 6px;">
                        <select id="productSelector" size="8" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            <option value="">请选择产品</option>
                        </select>
                    </div>
                    <button type="button" onclick="addProductAssociation()" style="padding: 8px 15px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">添加关联</button>
                </div>
            </div>

            <!-- 已关联产品列表 -->
            <div style="margin-bottom: 20px;">
                <h4 style="margin-bottom: 15px;">已关联产品</h4>
                <div id="associatedProductsList" style="max-height: 300px; overflow-y: auto;">
                    <!-- 关联产品列表将在这里显示 -->
                </div>
            </div>

            <div style="text-align: right;">
                <button type="button" class="btn btn-secondary" onclick="closeCarModelProductsModal()">关闭</button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="../js/supabase-config.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="js/admin-common.js"></script>
    <script>
        let allCarModels = [];
        let filteredCarModels = [];

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAdminAuth()) {
                loadCarModels();
                loadBrands();
                initializeFilters();
                loadProducts(); // 加载产品数据用于关联
            }
        });

        // 加载车型数据
        async function loadCarModels() {
            try {
                console.log('开始加载车型数据...');

                // 加载车型数据并包含关联产品数量
                const { data: carModels, error } = await supabase
                    .from('car_models')
                    .select(`
                        *,
                        product_car_models (
                            id
                        )
                    `)
                    .order('brand', { ascending: true });

                if (error) {
                    console.error('加载车型数据失败:', error);
                    showError('加载车型数据失败: ' + error.message);
                    return;
                }

                allCarModels = carModels || [];
                filteredCarModels = [...allCarModels];

                displayCarModels();
                updateStats();

            } catch (error) {
                console.error('加载车型失败:', error);
                showError('加载车型数据失败');
            }
        }

        // 显示车型列表
        function displayCarModels() {
            const tbody = document.getElementById('car-models-tbody');

            if (filteredCarModels.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; padding: 40px; color: #666;">暂无车型数据</td></tr>';
                return;
            }

            let html = '';
            filteredCarModels.forEach(carModel => {
                const productCount = carModel.product_car_models ? carModel.product_car_models.length : 0;
                html += `
                    <tr class="car-model-row">
                        <td>${carModel.brand || ''}</td>
                        <td>${carModel.model || ''}</td>
                        <td>${productCount}</td>
                        <td>
                            <span style="color: #28a745;">启用</span>
                        </td>
                        <td>
                            <button class="btn btn-secondary" onclick="editCarModel('${carModel.id}')" style="margin-right: 5px;">编辑</button>
                            <button class="btn btn-info" onclick="viewCarModelProducts('${carModel.id}')" style="margin-right: 5px;">关联产品</button>
                            <button class="btn btn-danger" onclick="deleteCarModel('${carModel.id}')">删除</button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
            document.getElementById('car-models-count').textContent = filteredCarModels.length;
        }

        // 更新统计信息
        function updateStats() {
            const totalCarModels = allCarModels.length;
            const totalBrands = new Set(allCarModels.map(model => model.brand)).size;
            const linkedModels = allCarModels.filter(model =>
                model.product_car_models && model.product_car_models.length > 0
            ).length;

            document.getElementById('total-car-models').textContent = totalCarModels;
            document.getElementById('total-brands').textContent = totalBrands;
            document.getElementById('linked-models').textContent = linkedModels;
        }

        // 加载品牌列表
        async function loadBrands() {
            try {
                const { data: carModels, error } = await supabase
                    .from('car_models')
                    .select('brand')
                    .order('brand', { ascending: true });

                if (error) {
                    console.error('加载品牌失败:', error);
                    return;
                }

                // 获取唯一品牌
                const brands = [...new Set(carModels.map(item => item.brand))];
                const brandSelect = document.getElementById('brand-filter');

                // 清空现有选项（保留默认选项）
                brandSelect.innerHTML = '<option value="">选择品牌</option>';

                // 添加品牌选项
                brands.forEach(brand => {
                    const option = document.createElement('option');
                    option.value = brand;
                    option.textContent = brand;
                    brandSelect.appendChild(option);
                });

            } catch (error) {
                console.error('加载品牌失败:', error);
            }
        }

        // 根据品牌加载车型
        async function loadModelsByBrand(brand) {
            const modelSelect = document.getElementById('model-filter');

            if (!brand) {
                modelSelect.innerHTML = '<option value="">选择车型</option>';
                modelSelect.disabled = true;
                return;
            }

            try {
                const { data: carModels, error } = await supabase
                    .from('car_models')
                    .select('model')
                    .eq('brand', brand)
                    .order('model', { ascending: true });

                if (error) {
                    console.error('加载车型失败:', error);
                    return;
                }

                // 获取唯一车型
                const models = [...new Set(carModels.map(item => item.model))];

                // 清空现有选项
                modelSelect.innerHTML = '<option value="">选择车型</option>';

                // 添加车型选项
                models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model;
                    option.textContent = model;
                    modelSelect.appendChild(option);
                });

                modelSelect.disabled = false;

            } catch (error) {
                console.error('加载车型失败:', error);
            }
        }

        // 初始化筛选器事件
        function initializeFilters() {
            // 品牌选择事件
            document.getElementById('brand-filter').addEventListener('change', function() {
                const selectedBrand = this.value;
                loadModelsByBrand(selectedBrand);
                // 自动触发搜索
                searchCarModels();
            });

            // 车型选择事件
            document.getElementById('model-filter').addEventListener('change', function() {
                searchCarModels();
            });

            // 产品ID输入事件
            document.getElementById('product-id-filter').addEventListener('input', function() {
                // 延迟搜索，避免过于频繁
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    searchCarModels();
                }, 500);
            });
        }

        // 搜索车型
        function searchCarModels() {
            const brandFilter = document.getElementById('brand-filter').value.trim();
            const modelFilter = document.getElementById('model-filter').value.trim();
            const productIdFilter = document.getElementById('product-id-filter').value.trim().toLowerCase();

            if (!brandFilter && !modelFilter && !productIdFilter) {
                filteredCarModels = [...allCarModels];
            } else {
                filteredCarModels = allCarModels.filter(carModel => {
                    // 品牌筛选
                    const brandMatch = !brandFilter || carModel.brand === brandFilter;

                    // 车型筛选
                    const modelMatch = !modelFilter || carModel.model === modelFilter;

                    // 产品ID筛选（通过查找products表中car_models字段包含该车型的记录）
                    let productIdMatch = true;
                    if (productIdFilter) {
                        // 这里可以扩展为查询products表，暂时先简单匹配
                        productIdMatch = (carModel.id && carModel.id.toString().includes(productIdFilter)) ||
                                       (carModel.brand && carModel.brand.toLowerCase().includes(productIdFilter)) ||
                                       (carModel.model && carModel.model.toLowerCase().includes(productIdFilter));
                    }

                    return brandMatch && modelMatch && productIdMatch;
                });
            }

            displayCarModels();
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('brand-filter').value = '';
            document.getElementById('model-filter').value = '';
            document.getElementById('model-filter').disabled = true;
            document.getElementById('product-id-filter').value = '';
            filteredCarModels = [...allCarModels];
            displayCarModels();
        }

        // 显示添加车型模态框
        function showAddCarModelModal() {
            document.getElementById('modalTitle').textContent = '添加车型';
            document.getElementById('carModelForm').reset();
            document.getElementById('carModelId').value = '';
            document.getElementById('carModelModal').style.display = 'block';
        }

        // 关闭车型模态框
        function closeCarModelModal() {
            document.getElementById('carModelModal').style.display = 'none';
        }

        // 保存车型
        async function saveCarModel() {
            const carModelId = document.getElementById('carModelId').value;
            const brand = document.getElementById('brand').value.trim();
            const model = document.getElementById('model').value.trim();

            if (!brand || !model) {
                alert('请填写品牌和车型');
                return;
            }

            try {
                let result;
                if (carModelId) {
                    // 更新车型
                    result = await supabase
                        .from('car_models')
                        .update({
                            brand: brand,
                            model: model,
                            updated_at: new Date().toISOString()
                        })
                        .eq('id', carModelId);
                } else {
                    // 添加新车型
                    result = await supabase
                        .from('car_models')
                        .insert({
                            brand: brand,
                            model: model
                        });
                }

                if (result.error) throw result.error;

                showSuccess(carModelId ? '车型更新成功' : '车型添加成功');
                closeCarModelModal();
                loadCarModels();

            } catch (error) {
                console.error('保存车型失败:', error);
                showError('保存车型失败: ' + error.message);
            }
        }

        // 编辑车型
        function editCarModel(carModelId) {
            const carModel = allCarModels.find(model => model.id == carModelId);
            if (!carModel) {
                showError('车型不存在');
                return;
            }

            document.getElementById('modalTitle').textContent = '编辑车型';
            document.getElementById('carModelId').value = carModel.id;
            document.getElementById('brand').value = carModel.brand || '';
            document.getElementById('model').value = carModel.model || '';

            document.getElementById('carModelModal').style.display = 'block';
        }

        // 删除车型
        async function deleteCarModel(carModelId) {
            if (!confirm('确定要删除这个车型吗？此操作不可恢复。')) {
                return;
            }

            try {
                const { error } = await supabase
                    .from('car_models')
                    .delete()
                    .eq('id', carModelId);

                if (error) throw error;

                showSuccess('车型删除成功');
                loadCarModels();

            } catch (error) {
                console.error('删除车型失败:', error);
                showError('删除车型失败: ' + error.message);
            }
        }

        // 下载导入模板
        function downloadTemplate() {
            // 创建模板数据
            const templateData = [
                ['品牌', '车型', '关联产品数据ID(逗号分隔)'],
                ['奥迪', 'A4L', '1,2,3'],
                ['宝马', '320i', ''],
                ['大众', '迈腾', '5'],
                ['特斯拉', 'Model 3', '']
            ];

            // 创建工作簿
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.aoa_to_sheet(templateData);

            // 设置列宽
            ws['!cols'] = [
                { width: 15 },  // 品牌
                { width: 20 },  // 车型
                { width: 24 }   // 关联产品数据ID(逗号分隔)
            ];

            // 添加工作表
            XLSX.utils.book_append_sheet(wb, ws, '车型导入模板');

            // 下载文件
            XLSX.writeFile(wb, '车型导入模板.xlsx');
        }

        // 导出车型
        function exportCarModels() {
            if (allCarModels.length === 0) {
                showError('没有车型数据可导出');
                return;
            }

            // 准备导出数据
            const exportData = [
                ['ID', '品牌', '车型', '创建时间']
            ];

            allCarModels.forEach(carModel => {
                exportData.push([
                    carModel.id,
                    carModel.brand || '',
                    carModel.model || '',
                    carModel.created_at ? new Date(carModel.created_at).toLocaleString() : ''
                ]);
            });

            // 创建工作簿
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.aoa_to_sheet(exportData);

            // 设置列宽
            ws['!cols'] = [
                { width: 8 },   // ID
                { width: 15 },  // 品牌
                { width: 20 },  // 车型
                { width: 20 }   // 创建时间
            ];

            // 添加工作表
            XLSX.utils.book_append_sheet(wb, ws, '车型数据');

            // 下载文件
            const fileName = `车型数据_${new Date().toISOString().slice(0, 10)}.xlsx`;
            XLSX.writeFile(wb, fileName);

            showSuccess('车型数据导出成功');
        }

        // 显示成功消息
        function showSuccess(message) {
            alert('✅ ' + message);
        }

        // 显示错误消息
        function showError(message) {
            alert('❌ ' + message);
        }

        // 显示导入模态框
        function showImportModal() {
            document.getElementById('importFile').value = '';
            document.getElementById('importProgress').style.display = 'none';
            document.getElementById('importModal').style.display = 'block';
        }

        // 关闭导入模态框
        function closeImportModal() {
            document.getElementById('importModal').style.display = 'none';
        }

        // 批量导入车型
        async function importCarModels() {
            const fileInput = document.getElementById('importFile');
            const file = fileInput.files[0];

            if (!file) {
                showError('请选择要导入的Excel文件');
                return;
            }

            const progressDiv = document.getElementById('importProgress');
            const progressBar = document.getElementById('progressBar');
            const statusDiv = document.getElementById('importStatus');

            try {
                progressDiv.style.display = 'block';
                progressBar.style.width = '10%';
                statusDiv.textContent = '正在读取文件...';

                // 读取Excel文件
                const data = await readExcelFile(file);

                if (!data || data.length < 2) {
                    throw new Error('Excel文件格式不正确或没有数据');
                }

                progressBar.style.width = '30%';
                statusDiv.textContent = '正在验证数据...';

                // 验证数据格式
                const headers = data[0];
                if (!headers.includes('品牌') || !headers.includes('车型')) {
                    throw new Error('Excel文件必须包含"品牌"和"车型"列');
                }

                const brandIndex = headers.indexOf('品牌');
                const modelIndex = headers.indexOf('车型');
                const productDataIdsIndex = headers.indexOf('关联产品数据ID(逗号分隔)');

                // 处理数据
                const importData = [];
                const errors = [];

                for (let i = 1; i < data.length; i++) {
                    const row = data[i];
                    const brand = row[brandIndex]?.toString().trim();
                    const model = row[modelIndex]?.toString().trim();

                    if (!brand || !model) {
                        errors.push(`第${i + 1}行：品牌和车型不能为空`);
                        continue;
                    }

                    // 解析关联产品数据ID列表（可选）
                    let productDataIds = [];
                    if (productDataIdsIndex !== -1 && row[productDataIdsIndex]) {
                        productDataIds = row[productDataIdsIndex]
                            .toString()
                            .split(',')
                            .map(s => s.trim())
                            .filter(s => s !== '');
                    }

                    importData.push({
                        brand: brand,
                        model: model,
                        productDataIds: productDataIds
                    });
                }

                if (errors.length > 0) {
                    throw new Error('数据验证失败：\n' + errors.join('\n'));
                }

                progressBar.style.width = '50%';
                statusDiv.textContent = `正在导入 ${importData.length} 条记录...`;

                // 批量插入数据
                let successCount = 0;
                let skipCount = 0;

                for (let i = 0; i < importData.length; i++) {
                    try {
                        // 插入车型
                        const { data: inserted, error } = await supabase
                            .from('car_models')
                            .insert({
                                brand: importData[i].brand,
                                model: importData[i].model
                            })
                            .select('id')
                            .single();

                        if (error) {
                            if (error.code === '23505') { // 唯一约束冲突
                                skipCount++;
                                continue;
                            } else {
                                throw error;
                            }
                        }

                        const carModelId = inserted?.id;

                        // 如果有产品数据ID列表，通过数据ID匹配产品，再写入关联表
                        if (carModelId && importData[i].productDataIds && importData[i].productDataIds.length > 0) {
                            const { data: productsByDataId, error: findErr } = await supabase
                                .from('products')
                                .select('id, data_id')
                                .in('data_id', importData[i].productDataIds);
                            if (findErr) {
                                console.warn('按数据ID查询产品失败:', findErr.message);
                            } else if (productsByDataId && productsByDataId.length > 0) {
                                const associations = productsByDataId.map(p => ({
                                    product_id: p.id,
                                    car_model_id: carModelId
                                }));
                                const { error: assocErr } = await supabase
                                    .from('product_car_models')
                                    .insert(associations);
                                if (assocErr) {
                                    console.warn(`车型ID ${carModelId} 的关联产品写入失败:`, assocErr.message);
                                }
                            }
                        }

                        successCount++;

                        // 更新进度
                        const progress = 50 + (i + 1) / importData.length * 40;
                        progressBar.style.width = progress + '%';
                        statusDiv.textContent = `已处理 ${i + 1}/${importData.length} 条记录...`;

                    } catch (error) {
                        console.error('导入第' + (i + 1) + '条记录失败:', error);
                        errors.push(`第${i + 1}条记录导入失败: ${error.message}`);
                    }
                }

                progressBar.style.width = '100%';
                statusDiv.textContent = `导入完成！成功：${successCount}条，跳过：${skipCount}条`;

                if (errors.length > 0) {
                    console.warn('导入过程中的错误:', errors);
                }

                setTimeout(() => {
                    closeImportModal();
                    showSuccess(`导入完成！成功导入${successCount}条记录，跳过${skipCount}条重复记录`);
                    loadCarModels(); // 重新加载数据
                }, 2000);

            } catch (error) {
                console.error('批量导入失败:', error);
                progressDiv.style.display = 'none';
                showError('批量导入失败: ' + error.message);
            }
        }

        // 读取Excel文件
        function readExcelFile(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const data = new Uint8Array(e.target.result);
                        const workbook = XLSX.read(data, { type: 'array' });
                        const firstSheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheetName];
                        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                        resolve(jsonData);
                    } catch (error) {
                        reject(error);
                    }
                };
                reader.onerror = function() {
                    reject(new Error('文件读取失败'));
                };
                reader.readAsArrayBuffer(file);
            });
        }

        // 产品数据
        let allProducts = [];

        // 加载产品数据
        async function loadProducts() {
            try {
                const { data: products, error } = await supabase
                    .from('products')
                    .select('id, data_id, product_name, stock_code')
                    .order('product_name', { ascending: true });

                if (error) {
                    console.error('加载产品失败:', error);
                    return;
                }

                allProducts = products || [];
                populateProductSelector();

            } catch (error) {
                console.error('加载产品失败:', error);
            }
        }

        // 填充产品选择器（展示数据ID）
        function populateProductSelector() {
            const selector = document.getElementById('productSelector');
            selector.innerHTML = '<option value="">请选择产品</option>';

            (allProducts || []).forEach(product => {
                const option = document.createElement('option');
                option.value = product.id; // 仍以内部id提交
                const displayId = product.data_id || product.stock_code || product.id;
                option.textContent = `${product.product_name} [数据ID: ${displayId}]`;
                selector.appendChild(option);
            });
        }

        // 产品下拉快速筛选
        function filterProductOptions() {
            const input = document.getElementById('productSearch');
            const keyword = (input.value || '').toLowerCase();
            const selector = document.getElementById('productSelector');

            // 先清空
            selector.innerHTML = '<option value="">请选择产品</option>';

            // 过滤 allProducts（按数据ID或产品名）
            const filtered = (allProducts || []).filter(p => {
                const dataId = (p.data_id || '').toLowerCase();
                const name = (p.product_name || '').toLowerCase();
                const stock = (p.stock_code || '').toLowerCase();
                return !keyword || dataId.includes(keyword) || name.includes(keyword) || stock.includes(keyword);
            });

            // 重新渲染选项
            filtered.forEach(product => {
                const option = document.createElement('option');
                option.value = product.id;
                const displayId = product.data_id || product.stock_code || product.id;
                option.textContent = `${product.product_name} [数据ID: ${displayId}]`;
                selector.appendChild(option);
            });
        }

        // 查看车型关联产品
        async function viewCarModelProducts(carModelId) {
            const carModel = allCarModels.find(model => model.id == carModelId);
            if (!carModel) {
                showError('车型不存在');
                return;
            }

            // 设置车型信息
            document.getElementById('carModelProductsTitle').textContent = `${carModel.brand} ${carModel.model} - 关联产品管理`;
            document.getElementById('carModelInfo').innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <div><strong>品牌：</strong>${carModel.brand}</div>
                    <div><strong>车型：</strong>${carModel.model}</div>
                </div>
            `;

            // 存储当前车型ID
            window.currentCarModelId = carModelId;

            // 先显示模态框，避免数据请求失败导致看起来“没反应”
            document.getElementById('carModelProductsModal').style.display = 'block';

            // 加载关联产品
            await loadAssociatedProducts(carModelId);
        }

        // 加载关联产品
        async function loadAssociatedProducts(carModelId) {
            try {
                const { data: associations, error } = await supabase
                    .from('product_car_models')
                    .select(`
                        id,
                        product_id,
                        products (
                            id,
                            product_name,
                            stock_code
                        )
                    `)
                    .eq('car_model_id', carModelId);

                if (error) {
                    console.error('加载关联产品失败:', error);
                    return;
                }

                displayAssociatedProducts(associations || []);

            } catch (error) {
                console.error('加载关联产品失败:', error);
            }
        }

        // 显示关联产品
        function displayAssociatedProducts(associations) {
            const container = document.getElementById('associatedProductsList');

            if (associations.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">暂无关联产品</div>';
                return;
            }

            container.innerHTML = associations.map(assoc => {
                const product = assoc.products;
                return `
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 10px;">
                        <div>
                            <strong>${product.product_name}</strong>
                            <span style="color: #666; margin-left: 10px;">(${product.stock_code || product.id})</span>
                        </div>
                        <button onclick="removeProductAssociation(${assoc.id})" style="padding: 4px 8px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">移除</button>
                    </div>
                `;
            }).join('');
        }

        // 添加产品关联
        async function addProductAssociation() {
            const productId = document.getElementById('productSelector').value;
            const carModelId = window.currentCarModelId;

            if (!productId) {
                showError('请选择要关联的产品');
                return;
            }

            try {
                // 检查是否已存在关联
                const { data: existing, error: checkError } = await supabase
                    .from('product_car_models')
                    .select('id')
                    .eq('product_id', productId)
                    .eq('car_model_id', carModelId);

                if (checkError) throw checkError;

                if (existing && existing.length > 0) {
                    showError('该产品已与此车型关联');
                    return;
                }

                // 添加关联
                const { error } = await supabase
                    .from('product_car_models')
                    .insert({
                        product_id: productId,
                        car_model_id: carModelId
                    });

                if (error) throw error;

                // 同步更新产品表car_models聚合字段
                await updateProductCarModelsString(productId);

                showSuccess('产品关联添加成功');

                // 重置选择器
                document.getElementById('productSelector').value = '';

                // 重新加载关联产品
                await loadAssociatedProducts(carModelId);

                // 重新加载车型数据以更新关联产品数
                await loadCarModels();

            } catch (error) {
                console.error('添加产品关联失败:', error);
                showError('添加产品关联失败: ' + error.message);
            }
        }

        // 移除产品关联
        async function removeProductAssociation(associationId) {
            if (!confirm('确定要移除这个产品关联吗？')) {
                return;
            }

            try {
                // 在删除前先查出该关联的product_id
                const { data: assocBefore, error: fetchErr } = await supabase
                    .from('product_car_models')
                    .select('product_id')
                    .eq('id', associationId)
                    .single();
                if (fetchErr) throw fetchErr;
                const productId = assocBefore?.product_id;

                const { error } = await supabase
                    .from('product_car_models')
                    .delete()
                    .eq('id', associationId);

                if (error) throw error;

                // 同步更新产品表car_models聚合字段
                if (productId) {
                    await updateProductCarModelsString(productId);
                }

                showSuccess('产品关联移除成功');

                // 重新加载关联产品
                await loadAssociatedProducts(window.currentCarModelId);

                // 重新加载车型数据以更新关联产品数
                await loadCarModels();

            } catch (error) {
                console.error('移除产品关联失败:', error);
                showError('移除产品关联失败: ' + error.message);
            }
        }

        // 计算并更新某产品的car_models聚合文本
        async function updateProductCarModelsString(productId) {
            try {
                // 取该产品的所有car_model_id
                const { data: pcm, error: e1 } = await supabase
                    .from('product_car_models')
                    .select('car_model_id')
                    .eq('product_id', productId);
                if (e1) throw e1;

                let agg = '';
                if (pcm && pcm.length > 0) {
                    const ids = pcm.map(x => x.car_model_id);
                    const { data: cms, error: e2 } = await supabase
                        .from('car_models')
                        .select('brand, model')
                        .in('id', ids);
                    if (e2) throw e2;
                    agg = (cms || []).map(cm => `${cm.brand}${cm.model}`).join(',');
                }

                // 更新products表car_models字段
                const { error: e3 } = await supabase
                    .from('products')
                    .update({ car_models: agg })
                    .eq('id', productId);
                if (e3) throw e3;
            } catch (err) {
                console.warn('更新产品car_models聚合字段失败:', err?.message || err);
            }
        }

        // 关闭车型产品模态框
        function closeCarModelProductsModal() {
            document.getElementById('carModelProductsModal').style.display = 'none';
            window.currentCarModelId = null;
        }

        // 点击模态框外部关闭
        document.getElementById('carModelModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCarModelModal();
            }
        });

        document.getElementById('importModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeImportModal();
            }
        });

        document.getElementById('carModelProductsModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCarModelProductsModal();
            }
        });
    </script>
</body>
</html>
