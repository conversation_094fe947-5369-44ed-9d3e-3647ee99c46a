﻿/* hover-effect */

/* ----------------Direction Effect begin------------------------- */
.hover-effect[data-effect-name="direction-top"] img,
.hover-effect[data-effect-name="direction-right"] img,
.hover-effect[data-effect-name="direction-bottom"] img,
.hover-effect[data-effect-name="direction-left"] img,
.hover-effect[data-effect-name="direction-origin"] img {
    -webkit-transition: all .3s ease-in-out;
    -o-transition: all .3s ease-in-out;
    -moz-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out
}
/* .layer common */
.hover-effect[data-effect-name="direction-top"] .layer,
.hover-effect[data-effect-name="direction-right"] .layer,
.hover-effect[data-effect-name="direction-bottom"] .layer,
.hover-effect[data-effect-name="direction-left"] .layer,
.hover-effect[data-effect-name="direction-origin"] .layer,
.hover-effect[data-effect-name="zoom-in"] .layer,
.hover-effect[data-effect-name="flash-full"] .layer,
.hover-effect[data-effect-name="flash-line"] .layer,
.hover-effect[data-effect-name="flash-disperse"] .layer,
.hover-effect[data-effect-name="xxx"] .layer {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    -webkit-transition: all .3s ease-in-out;
    -o-transition: all .3s ease-in-out;
    -moz-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: -moz-box;
    display: flex;
    -ms-flex-align: center;
    -webkit-align-items: center;
    -moz-box-align: center;
    align-items: center;
    -ms-flex-line-pack: center;
    -webkit-align-content: center;
    align-content: center;
    -ms-flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    pointer-events: none
}

.hover-effect[data-effect-name="direction-top"] .layer {
    top: -100%;
    left: 0;
}

.hover-effect[data-effect-name="direction-right"] .layer {
    top: 0;
    right: -100%;
}

.hover-effect[data-effect-name="direction-bottom"] .layer {
    bottom: -100%;
    left: 0;
}

.hover-effect[data-effect-name="direction-left"] .layer {
    left: -100%;
    top: 0;
}

.hover-effect[data-effect-name="direction-origin"] .layer {
    bottom: 0;
    left: 0;
}

.hover-effect[data-effect-name="direction-top"]:hover img,
.hover-effect[data-effect-name="direction-right"]:hover img,
.hover-effect[data-effect-name="direction-bottom"]:hover img,
.hover-effect[data-effect-name="direction-left"]:hover img,
.hover-effect[data-effect-name="direction-origin"]:hover img {
    -webkit-transform: scale(1.2);
    -moz-transform: scale(1.2);
    -ms-transform: scale(1.2);
    -o-transform: scale(1.2);
    transform: scale(1.2)
}

.hover-effect[data-effect-name="direction-top"]:hover .layer,
.hover-effect[data-effect-name="direction-right"]:hover .layer,
.hover-effect[data-effect-name="direction-bottom"]:hover .layer,
.hover-effect[data-effect-name="direction-left"]:hover .layer,
.hover-effect[data-effect-name="direction-origin"]:hover .layer {
    opacity: 1
}

.hover-effect[data-effect-name="direction-top"]:hover .layer {
    top: 0;
}

.hover-effect[data-effect-name="direction-right"]:hover .layer {
    right: 0;
}

.hover-effect[data-effect-name="direction-bottom"]:hover .layer {
    bottom: 0;
}

.hover-effect[data-effect-name="direction-left"]:hover .layer {
    left: 0;
}
/* ----------------Direction Effect end------------------------- */

/* ------Border Effect begin--------------- */
.hover-effect[data-effect-name="extend-border"] img,
.hover-effect[data-effect-name="full-border"] img {
    -webkit-transition: all .3s ease-in-out;
    -o-transition: all .3s ease-in-out;
    -moz-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out
}

.hover-effect[data-effect-name="extend-border"] .layer,
.hover-effect[data-effect-name="full-border"] .layer {
    position: absolute;
    left: 0;
    top: 0;
    padding: 30px;
    width: 100%;
    height: 100%;
    -webkit-transition: all .3s ease-in-out;
    -o-transition: all .3s ease-in-out;
    -moz-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: -moz-box;
    display: flex;
    -ms-flex-align: center;
    -webkit-align-items: center;
    -moz-box-align: center;
    align-items: center;
    -ms-flex-line-pack: center;
    -webkit-align-content: center;
    align-content: center;
    -ms-flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    pointer-events: none
}

.hover-effect[data-effect-name="extend-border"] .title,
.hover-effect[data-effect-name="full-border"] .title,
.hover-effect[data-effect-name="extend-border"] .desc,
.hover-effect[data-effect-name="full-border"] .desc {
    -webkit-transition: all .3s ease-in-out;
    -o-transition: all .3s ease-in-out;
    -moz-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out
}

.hover-effect[data-effect-name="extend-border"] .title,
.hover-effect[data-effect-name="full-border"] .title {
    padding-bottom: 42px
}

.hover-effect[data-effect-name="extend-border"] .title,
.hover-effect[data-effect-name="full-border"] .title,
.hover-effect[data-effect-name="extend-border"] .desc,
.hover-effect[data-effect-name="full-border"] .desc {
    opacity: 0
}

/* -------extend-border" 伪类begin---------- */
.hover-effect[data-effect-name="extend-border"]::before {
    border-top: 1px solid #fff;
    border-bottom: 1px solid #fff;
    -webkit-transform: scale(0, 1);
    -moz-transform: scale(0, 1);
    -ms-transform: scale(0, 1);
    -o-transform: scale(0, 1);
    transform: scale(0, 1)
}

.hover-effect[data-effect-name="extend-border"]::after {
    border-left: 1px solid #fff;
    border-right: 1px solid #fff;
    -webkit-transform: scale(1, 0);
    -moz-transform: scale(1, 0);
    -ms-transform: scale(1, 0);
    -o-transform: scale(1, 0);
    transform: scale(1, 0)
}

.hover-effect[data-effect-name="extend-border"]::before,
.hover-effect[data-effect-name="extend-border"]::after {
    position: absolute;
    top: 30px;
    right: 30px;
    bottom: 30px;
    left: 30px;
    content: '';
    opacity: 0;
    -webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
    transition: opacity 0.35s, -webkit-transform 0.35s;
    -o-transition: opacity 0.35s, -o-transform 0.35s;
    -moz-transition: opacity 0.35s, transform 0.35s, -moz-transform 0.35s;
    transition: opacity 0.35s, transform 0.35s;
    transition: opacity 0.35s, transform 0.35s, -webkit-transform 0.35s, -moz-transform 0.35s, -o-transform 0.35s;
    transition: opacity 0.35s, transform 0.35s, -webkit-transform 0.35s;
    z-index: 2
}
/* -------extend-border" 伪类end---------- */

/* -------full-border" 伪类begin---------- */
.hover-effect[data-effect-name="full-border"]::before {
    position: absolute;
    top: 30px;
    right: 30px;
    bottom: 30px;
    left: 30px;
    content: '';
    opacity: 0;
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);
    border: 1px solid #fff;
    -webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
    transition: opacity 0.35s, -webkit-transform 0.35s;
    -o-transition: opacity 0.35s, -o-transform 0.35s;
    -moz-transition: opacity 0.35s, transform 0.35s, -moz-transform 0.35s;
    transition: opacity 0.35s, transform 0.35s;
    transition: opacity 0.35s, transform 0.35s, -webkit-transform 0.35s, -moz-transform 0.35s, -o-transform 0.35s;
    transition: opacity 0.35s, transform 0.35s, -webkit-transform 0.35s;
    z-index: 2
}
/* -------full-border" 伪类end---------- */
.hover-effect[data-effect-name="extend-border"]:hover::before,
.hover-effect[data-effect-name="extend-border"]:hover::after,
.hover-effect[data-effect-name="full-border"]:hover::before,
.hover-effect[data-effect-name="full-border"]:hover::after {
    opacity: 1;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1)
}

.hover-effect[data-effect-name="extend-border"]:hover img,
.hover-effect[data-effect-name="full-border"]:hover img {
    -webkit-transform: scale(1.2);
    -moz-transform: scale(1.2);
    -ms-transform: scale(1.2);
    -o-transform: scale(1.2);
    transform: scale(1.2)
}



.hover-effect[data-effect-name="extend-border"]:hover .title,
.hover-effect[data-effect-name="full-border"]:hover .title {
    -webkit-transform: translateY(16px);
    -moz-transform: translateY(16px);
    -ms-transform: translateY(16px);
    -o-transform: translateY(16px);
    transform: translateY(16px);
    opacity: 1
}

.hover-effect[data-effect-name="extend-border"]:hover .desc,
.hover-effect[data-effect-name="full-border"]:hover .desc {
    -webkit-transform: translateY(-16px);
    -moz-transform: translateY(-16px);
    -ms-transform: translateY(-16px);
    -o-transform: translateY(-16px);
    transform: translateY(-16px);
    opacity: 1
}
/* ------------Border Effect end-------------------- */



/* --------------Zoom Effect begin----------------------- */
.hover-effect[data-effect-name="zoom-in"] img {
    -webkit-transition: -webkit-transform .3s ease-in-out;
    transition: -webkit-transform .3s ease-in-out;
    -o-transition: -o-transform .3s ease-in-out;
    -moz-transition: transform .3s ease-in-out, -moz-transform .3s ease-in-out;
    transition: transform .3s ease-in-out;
    transition: transform .3s ease-in-out, -webkit-transform .3s ease-in-out, -moz-transform .3s ease-in-out, -o-transform .3s ease-in-out;
    transition: transform .3s ease-in-out
}

.hover-effect[data-effect-name="zoom-in"]:hover img {
    -webkit-transform: scale(1.2);
    -moz-transform: scale(1.2);
    -ms-transform: scale(1.2);
    -o-transform: scale(1.2);
    transform: scale(1.2)
}
/* --------------Zoom Effect end--------------------- */



.hover-effect[data-effect-name="zoom-in"] .layer,
.hover-effect[data-effect-name="flash-full"] .layer,
.hover-effect[data-effect-name="flash-line"] .layer,
.hover-effect[data-effect-name="flash-disperse"] .layer,
.hover-effect[data-effect-name="xxx"] .layer {
    top: 0;
    opacity: 0;
    background: transparent;
}

.hover-effect[data-effect-name="zoom-in"]:hover .layer,
.hover-effect[data-effect-name="flash-full"]:hover .layer,
.hover-effect[data-effect-name="flash-line"]:hover .layer,
.hover-effect[data-effect-name="flash-disperse"]:hover .layer {
    background: transparent !important;
    opacity: 1;
}
.hover-effect[data-effect-name="xxx"]:hover .layer {
    opacity: 1;
}
/* --------------Flash Effect begin--------------------- */
.hover-effect[data-effect-name="flash-full"]:hover img {
    opacity: 1;
    -webkit-animation: flash 1.5s;
    -moz-animation: full 1.5s;
    -o-animation: full 1.5s;
    animation: full 1.5s
}

.hover-effect[data-effect-name="flash-line"]::before,
.hover-effect[data-effect-name="flash-shadow"]::before {
    position: absolute;
    top: 0;
    left: -100%;
    z-index: 2;
    display: block;
    content: '';
    width: 50%;
    height: 100%;
    background: -webkit-gradient(linear, left top, right top, from(rgba(255, 255, 255, 0)), to(rgba(255, 255, 255, 0.3)));
    background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
    background: -moz-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
    background: -o-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
    background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
    -webkit-transform: skewX(-25deg);
    -moz-transform: skewX(-25deg);
    -ms-transform: skewX(-25deg);
    -o-transform: skewX(-25deg);
    transform: skewX(-25deg)
}

.hover-effect[data-effect-name="flash-line"]:hover::before,
.hover-effect[data-effect-name="flash-shadow"]:hover::before {
    -webkit-animation: line .75s;
    -moz-animation: line .75s;
    -o-animation: line .75s;
    animation: line .75s
}

.hover-effect[data-effect-name="flash-disperse"]::before {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 2;
    display: block;
    content: '';
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 100%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    opacity: 0
}

.hover-effect[data-effect-name="flash-disperse"]:hover::before {
    -webkit-animation: disperse .75s;
    -moz-animation: disperse .75s;
    -o-animation: disperse .75s;
    animation: disperse .75s
}

@-webkit-keyframes full {
    0% {
        opacity: .4
    }

    100% {
        opacity: 1
    }
}

@-moz-keyframes full {
    0% {
        opacity: .4
    }

    100% {
        opacity: 1
    }
}

@-o-keyframes full {
    0% {
        opacity: .4
    }

    100% {
        opacity: 1
    }
}

@keyframes full {
    0% {
        opacity: .4
    }

    100% {
        opacity: 1
    }
}

@-webkit-keyframes line {
    100% {
        left: 125%
    }
}

@-moz-keyframes line {
    100% {
        left: 125%
    }
}

@-o-keyframes line {
    100% {
        left: 125%
    }
}

@keyframes line {
    100% {
        left: 125%
    }
}

@-webkit-keyframes disperse {
    0% {
        opacity: 1
    }

    40% {
        opacity: 1
    }

    100% {
        width: 200%;
        height: 200%;
        opacity: 0
    }
}

@-moz-keyframes disperse {
    0% {
        opacity: 1
    }

    40% {
        opacity: 1
    }

    100% {
        width: 200%;
        height: 200%;
        opacity: 0
    }
}

@-o-keyframes disperse {
    0% {
        opacity: 1
    }

    40% {
        opacity: 1
    }

    100% {
        width: 200%;
        height: 200%;
        opacity: 0
    }
}

@keyframes disperse {
    0% {
        opacity: 1
    }

    40% {
        opacity: 1
    }

    100% {
        width: 200%;
        height: 200%;
        opacity: 0
    }
}


/* --------------Flash Effect end--------------------- */



/* -------------------- Button Effect  bengin -------------------------- */
.hover-effect[data-effect-name="grow"],
.hover-effect[data-effect-name="shrink"],
.hover-effect[data-effect-name="sink"],
.hover-effect[data-effect-name="forward"],
.hover-effect[data-effect-name="backward"] {
    -webkit-transition: all 300ms ease-in-out;
    -o-transition: all 300ms ease-in-out;
    -moz-transition: all 300ms ease-in-out;
    transition: all 300ms ease-in-out;
}

    .hover-effect[data-effect-name="grow"]:hover {
        -webkit-transform: scale(1.1);
        -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
        -o-transform: scale(1.1);
        transform: scale(1.1);
    }

    .hover-effect[data-effect-name="shrink"]:hover {
        -webkit-transform: scale(0.9);
        -moz-transform: scale(0.9);
        -ms-transform: scale(0.9);
        -o-transform: scale(0.9);
        transform: scale(0.9);
    }

.hover-effect[data-effect-name="float"]:hover {
    -webkit-transform: translateY(-8px);
    -moz-transform: translateY(-8px);
    -ms-transform: translateY(-8px);
    -o-transform: translateY(-8px);
    transform: translateY(-8px)
}

.hover-effect[data-effect-name="sink"]:hover {
    -webkit-transform: translateY(8px);
    -moz-transform: translateY(8px);
    -ms-transform: translateY(8px);
    -o-transform: translateY(8px);
    transform: translateY(8px);
}

.hover-effect[data-effect-name="forward"]:hover {
    -webkit-transform: translateX(8px);
    -moz-transform: translateX(8px);
    -ms-transform: translateX(8px);
    -o-transform: translateX(8px);
    transform: translateX(8px);
}

.hover-effect[data-effect-name="backward"]:hover {
    -webkit-transform: translateX(-8px);
    -moz-transform: translateX(-8px);
    -ms-transform: translateX(-8px);
    -o-transform: translateX(-8px);
    transform: translateX(-8px);
}

.hover-effect[data-effect-name="sweep-to-right"]:hover {
    -webkit-transform: perspective(1px) translateZ(0);
    -moz-transform: perspective(1px) translateZ(0);
    transform: perspective(1px) translateZ(0);
    position: relative;
}

    .hover-effect[data-effect-name="sweep-to-right"]:hover:before {
        -webkit-transform: scaleX(1);
        -moz-transform: scaleX(1);
        -ms-transform: scaleX(1);
        -o-transform: scaleX(1);
        transform: scaleX(1);
    }

.hover-effect[data-effect-name="sweep-to-right"]:before {
    content: "";
    position: absolute;
    z-index: -1;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    -webkit-transform: scaleX(0);
    -moz-transform: scaleX(0);
    -ms-transform: scaleX(0);
    -o-transform: scaleX(0);
    transform: scaleX(0);
    -webkit-transform-origin: 0 50%;
    -moz-transform-origin: 0 50%;
    -ms-transform-origin: 0 50%;
    -o-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transition: all 0.3s ease-out;
    -o-transition: all 0.3s ease-out;
    -moz-transition: all 0.3s ease-out;
    transition: all 0.3s ease-out;
}

.hover-effect[data-effect-name="rectangle-out"]:hover {
    -webkit-transform: perspective(1px) translateZ(0);
    -moz-transform: perspective(1px) translateZ(0);
    transform: perspective(1px) translateZ(0);
    position: relative;
}

.hover-effect[data-effect-name="rectangle-out"]:before {
    content: "";
    position: absolute;
    z-index: -1;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);
    -webkit-transition: all 0.3s ease-out;
    -o-transition: all 0.3s ease-out;
    -moz-transition: all 0.3s ease-out;
    transition: all 0.3s ease-out;
}

.hover-effect[data-effect-name="rectangle-out"]:hover:before {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
}

.hover-effect[data-effect-name="shutter-out-horizontal"]:hover {
    -webkit-transform: perspective(1px) translateZ(0);
    -moz-transform: perspective(1px) translateZ(0);
    transform: perspective(1px) translateZ(0);
    position: relative;
    -webkit-transition-property: color;
    -o-transition-property: color;
    -moz-transition-property: color;
    transition-property: color;
    -webkit-transition-duration: 0.3s;
    -moz-transition-duration: 0.3s;
    -o-transition-duration: 0.3s;
    transition-duration: 0.3s;
}

.hover-effect[data-effect-name="shutter-out-horizontal"]:before {
    content: "";
    position: absolute;
    z-index: -1;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    -webkit-transform: scaleX(0);
    -moz-transform: scaleX(0);
    -ms-transform: scaleX(0);
    -o-transform: scaleX(0);
    transform: scaleX(0);
    -webkit-transform-origin: 50%;
    -moz-transform-origin: 50%;
    -ms-transform-origin: 50%;
    -o-transform-origin: 50%;
    transform-origin: 50%;
    -webkit-transition: all 0.3s ease-out;
    -o-transition: all 0.3s ease-out;
    -moz-transition: all 0.3s ease-out;
    transition: all 0.3s ease-out;
}

.hover-effect[data-effect-name="shutter-out-horizontal"]:hover:before {
    -webkit-transform: scaleX(1);
    -moz-transform: scaleX(1);
    -ms-transform: scaleX(1);
    -o-transform: scaleX(1);
    transform: scaleX(1);
}

.hover-effect[data-effect-name="shutter-out-vertical"]:hover {
    display: inline-block;
    vertical-align: middle;
    -webkit-transform: perspective(1px) translateZ(0);
    -moz-transform: perspective(1px) translateZ(0);
    transform: perspective(1px) translateZ(0);
    position: relative;
    background: #e1e1e1;
    -webkit-transition-property: color;
    -o-transition-property: color;
    -moz-transition-property: color;
    transition-property: color;
    -webkit-transition-duration: 0.3s;
    -moz-transition-duration: 0.3s;
    -o-transition-duration: 0.3s;
    transition-duration: 0.3s;
}

.hover-effect[data-effect-name="shutter-out-vertical"]:before {
    content: "";
    position: absolute;
    z-index: -1;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    -webkit-transform: scaleY(0);
    -moz-transform: scaleY(0);
    -ms-transform: scaleY(0);
    -o-transform: scaleY(0);
    transform: scaleY(0);
    -webkit-transform-origin: 50%;
    -moz-transform-origin: 50%;
    -ms-transform-origin: 50%;
    -o-transform-origin: 50%;
    transform-origin: 50%;
    -webkit-transition: all 0.3s ease-out;
    -o-transition: all 0.3s ease-out;
    -moz-transition: all 0.3s ease-out;
    transition: all 0.3s ease-out;
}

.hover-effect[data-effect-name="shutter-out-vertical"]:hover:before {
    -webkit-transform: scaleY(1);
    -moz-transform: scaleY(1);
    -ms-transform: scaleY(1);
    -o-transform: scaleY(1);
    transform: scaleY(1);
}

.hover-effect[data-effect-name="underline-from-center"]:hover {
    -webkit-transform: perspective(1px) translateZ(0);
    -moz-transform: perspective(1px) translateZ(0);
    transform: perspective(1px) translateZ(0);
    position: relative;
    overflow: hidden;
}

.hover-effect[data-effect-name="underline-from-center"]:before {
    content: "";
    position: absolute;
    z-index: -1;
    left: 51%;
    right: 51%;
    bottom: 0;
    height: 2px;
    -webkit-transition: all 0.3s ease-out;
    -o-transition: all 0.3s ease-out;
    -moz-transition: all 0.3s ease-out;
    transition: all 0.3s ease-out;
}

.hover-effect[data-effect-name="underline-from-center"]:hover:before {
    left: 0;
    right: 0;
}

.hover-effect[data-effect-name="overline-from-center"]:hover {
    display: inline-block;
    vertical-align: middle;
    -webkit-transform: perspective(1px) translateZ(0);
    -moz-transform: perspective(1px) translateZ(0);
    transform: perspective(1px) translateZ(0);
    box-shadow: 0 0 1px rgba(0, 0, 0, 0);
    position: relative;
    overflow: hidden;
}

.hover-effect[data-effect-name="overline-from-center"]:before {
    content: "";
    position: absolute;
    z-index: -1;
    left: 51%;
    right: 51%;
    top: 0;
    height: 2px;
    -webkit-transition: all 0.3s ease-out;
    -o-transition: all 0.3s ease-out;
    -moz-transition: all 0.3s ease-out;
    transition: all 0.3s ease-out;
}

.hover-effect[data-effect-name="overline-from-center"]:hover:before {
    left: 0;
    right: 0;
}

/* -------------------- Button  bengin -------------------------- */



/* -------------------------list begin--------------------------------------- */
.hover-effect[data-effect-name="mirror-opacity"] img,
.hover-effect[data-effect-name="opacity-border"],
.hover-effect[data-effect-name="float-border"],
.hover-effect[data-effect-name="float"],
.hover-effect[data-effect-name="float-shadow"],
.hover-effect[data-effect-name="shadow"],
.hover-effect[data-effect-name="border"],
.hover-effect[data-effect-name='puff'] {
    -webkit-transition: all 300ms ease-in-out;
    -o-transition: all 300ms ease-in-out;
    -moz-transition: all 300ms ease-in-out;
    transition: all 300ms ease-in-out;
}

.hover-effect[data-effect-name="mirror-opacity"]:hover img {
    opacity: 0.7;
}

.hover-effect[data-effect-name="shadow"]:hover {
    box-shadow: 0 5px 12px rgba(0,0,0,.1);
}

.hover-effect[data-effect-name="float-shadow"]:hover {
    -webkit-transform: translateY(-8px);
    -moz-transform: translateY(-8px);
    -ms-transform: translateY(-8px);
    -o-transform: translateY(-8px);
    transform: translateY(-8px);
    box-shadow: 0 5px 12px rgba(0,0,0,.1);
}

.hover-effect[data-effect-name="flash-shadow"]:hover {
    box-shadow: 0 5px 12px rgba(0,0,0,.1);
}

.hover-effect[data-effect-name="flash-shadow"] {
    position: relative;
}

.hover-effect[data-effect-name="opacity-border"]:hover {
    opacity: 0.7;
}

.hover-effect[data-effect-name="float-border"]:hover {
    -webkit-transform: translateY(-8px);
    -moz-transform: translateY(-8px);
    -ms-transform: translateY(-8px);
    -o-transform: translateY(-8px);
    transform: translateY(-8px);
}

.hover-effect[data-effect-name='puff']:hover {
    -webkit-transform: scale(1.05) translate3d(0, 0, 8px);
    -moz-transform: scale(1.05) translate3d(0, 0, 8px);
    transform: scale(1.05) translate3d(0, 0, 8px);
}

.hover-effect[data-effect-name='flash-line'] {
    position: relative;
}
/* -------------------------List Effect end--------------------------------------- */



.hover-effect[data-effect-name='direction-top'] > .smAreaC:last-of-type {
    opacity: 0;
    -webkit-transform: translateY(-100%);
    transform: translateY(-100%);
    pointer-events: none;
}

.hover-effect[data-effect-name='direction-top']:hover > .smAreaC:last-of-type {
    opacity: 1;
    z-index: 2;
    -webkit-transform: translateY(0);
    transform: translateY(0);
    pointer-events: auto;
}

.hover-effect[data-effect-name='direction-right'] > .smAreaC:last-of-type {
    opacity: 0;
    -webkit-transform: translateX(100%);
    transform: translateX(100%);
    pointer-events: none;
}

.hover-effect[data-effect-name='direction-right']:hover > .smAreaC:last-of-type {
    opacity: 1;
    z-index: 9999;
    -webkit-transform: translateX(0);
    transform: translateX(0);
    pointer-events: auto;
}

.hover-effect[data-effect-name='direction-bottom'] > .smAreaC:last-of-type {
    opacity: 0;
    -webkit-transform: translateY(100%);
    transform: translateY(100%);
    pointer-events: none;
}

.hover-effect[data-effect-name='direction-bottom']:hover > .smAreaC:last-of-type {
    opacity: 1;
    z-index: 9999;
    -webkit-transform: translateY(0);
    transform: translateY(0);
    pointer-events: auto;
}


.hover-effect[data-effect-name='direction-left'] > .smAreaC:last-of-type {
    opacity: 0;
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
    pointer-events: none;
}

.hover-effect[data-effect-name='direction-left']:hover > .smAreaC:last-of-type {
    opacity: 1;
    z-index: 9999;
    -webkit-transform: translateX(0);
    transform: translateX(0);
    pointer-events: auto;
}

.hover-effect[data-effect-name='fade-in'] > .smAreaC:last-of-type {
    opacity: 0
}

.hover-effect[data-effect-name='fade-in']:hover > .smAreaC:last-of-type {
    opacity: 1;
    z-index: 9999;
    -webkit-animation: fadeCenter 1s ease-in-out;
    animation: fadeCenter 1s ease-in-out
}

.hover-effect[data-effect-name='fade-out'] > .smAreaC:first-of-type {
    z-index: 2;
    -webkit-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out
}

.hover-effect[data-effect-name='fade-out']:hover > .smAreaC:first-of-type {
    -webkit-transform: scale(2);
    transform: scale(2);
    opacity: 0;
    z-index: -1;
}

.hover-effect[data-effect-name='fade-out']:hover > .smAreaC:last-of-type {
    z-index: 9999;
    opacity: 1;
}

.hover-effect[data-effect-name='filp-top'] > .smAreaC:last-of-type {
    opacity: 0;
    -webkit-transform-origin: 50% 0;
    transform-origin: 50% 0;
    -webkit-transform: perspective(900px) rotateX(180deg);
    transform: perspective(900px) rotateX(180deg);
    pointer-events: none;
}

.hover-effect[data-effect-name='filp-top']:hover > .smAreaC:last-of-type {
    opacity: 1;
    z-index: 9999;
    -webkit-transform: perspective(900px) rotateX(0);
    transform: perspective(900px) rotateX(0);
    -webkit-transition: all 1s ease-in-out;
    transition: all 1s ease-in-out;
    pointer-events: auto;
}

.hover-effect[data-effect-name='filp-right'] > .smAreaC:last-of-type {
    opacity: 0;
    -webkit-transform-origin: 100% 50%;
    transform-origin: 100% 50%;
    -webkit-transform: perspective(900px) rotateY(180deg);
    transform: perspective(900px) rotateY(180deg);
    pointer-events: none;
}

.hover-effect[data-effect-name='filp-right']:hover > .smAreaC:last-of-type {
    opacity: 1;
    z-index: 9999;
    -webkit-transform: perspective(900px) rotateY(0);
    transform: perspective(900px) rotateY(0);
    -webkit-transition: all 1s ease-in-out;
    transition: all 1s ease-in-out;
    pointer-events: auto;
}

.hover-effect[data-effect-name='filp-bottom'] > .smAreaC:last-of-type {
    opacity: 0;
    -webkit-transform-origin: 50% 100%;
    transform-origin: 50% 100%;
    -webkit-transform: perspective(900px) rotateX(-180deg);
    transform: perspective(900px) rotateX(-180deg);
    pointer-events: none;
}

.hover-effect[data-effect-name='filp-bottom']:hover > .smAreaC:last-of-type {
    opacity: 1;
    z-index: 9999;
    -webkit-transform: perspective(900px) rotateX(0);
    transform: perspective(900px) rotateX(0);
    -webkit-transition: all 1s ease-in-out;
    transition: all 1s ease-in-out;
    pointer-events: auto;
}

.hover-effect[data-effect-name='filp-left'] > .smAreaC:last-of-type {
    opacity: 0;
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transform: perspective(900px) rotateY(-180deg);
    transform: perspective(900px) rotateY(-180deg);
    pointer-events: none;
}

.hover-effect[data-effect-name='filp-left']:hover > .smAreaC:last-of-type {
    opacity: 1;
    z-index: 9999;
    -webkit-transform: perspective(900px) rotateY(0);
    transform: perspective(900px) rotateY(0);
    -webkit-transition: all 1s ease-in-out;
    transition: all 1s ease-in-out;
    pointer-events: auto;
}

.hover-effect[data-effect-name='filp-origin'] > .smAreaC:first-of-type {
    z-index: 3;
    -webkit-backface-visibility: visible;
    backface-visibility: visible;
    -webkit-transition: -webkit-transform 0.5s ease-in-out;
    transition: -webkit-transform 0.5s ease-in-out;
    transition: transform 0.5s ease-in-out;
    transition: transform 0.5s ease-in-out, -webkit-transform 0.5s ease-in-out
}

.hover-effect[data-effect-name='filp-origin']:hover > .smAreaC:first-of-type {
    opacity: 0;
    -webkit-animation: ladderin 0.5s ease-in-out;
    animation: ladderin 0.5s ease-in-out
}

.hover-effect[data-effect-name='filp-origin']:hover > .smAreaC:last-of-type {
    z-index: 9999;
    opacity: 1;
}

.hover-effect[data-effect-name='slash-fly-out'] > .smAreaC:first-of-type {
    z-index: 3;
    -webkit-backface-visibility: visible;
    backface-visibility: visible;
    -webkit-transition: -webkit-transform 0.5s ease-in-out;
    transition: -webkit-transform 0.5s ease-in-out;
    transition: transform 0.5s ease-in-out;
    transition: transform 0.5s ease-in-out, -webkit-transform 0.5s ease-in-out
}

.hover-effect[data-effect-name='slash-fly-out']:hover > .smAreaC:last-of-type {
    z-index: 9999;
    opacity: 1;
}

.hover-effect[data-effect-name='slash-fly-out']:hover > .smAreaC:first-of-type {
    -webkit-animation: bombout 1s both;
    animation: bombout 1s both;
    pointer-events: none;
}

@-webkit-keyframes fadeCenter {
    0% {
        opacity: 0;
        z-index: -1
    }

    40% {
        opacity: 1;
        z-index: 2;
        -webkit-transform: scale(1.2);
        transform: scale(1.2)
    }

    100% {
        opacity: 1;
        z-index: 2;
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@-webkit-keyframes ladderin {
    0% {
        opacity: 1;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: perspective(800px) rotate(0) translateZ(0);
        transform: perspective(800px) rotate(0) translateZ(0)
    }

    100% {
        opacity: 0;
        -webkit-transform-origin: 50% 0;
        transform-origin: 50% 0;
        -webkit-transform: perspective(800px) rotateY(180deg) translateZ(300px);
        transform: perspective(800px) rotateY(180deg) translateZ(300px)
    }
}

@keyframes ladderin {
    0% {
        opacity: 1;
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
        -webkit-transform: perspective(800px) rotate(0) translateZ(0);
        transform: perspective(800px) rotate(0) translateZ(0)
    }

    100% {
        opacity: 0;
        -webkit-transform-origin: 50% 0;
        transform-origin: 50% 0;
        -webkit-transform: perspective(800px) rotateY(180deg) translateZ(300px);
        transform: perspective(800px) rotateY(180deg) translateZ(300px)
    }
}

@-webkit-keyframes bombout {
    0% {
        opacity: 1;
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    50% {
        opacity: 1;
        -webkit-transform-origin: 200% 50%;
        transform-origin: 200% 50%;
        -webkit-transform: rotate(160deg);
        transform: rotate(160deg)
    }

    100% {
        opacity: 0;
        -webkit-transform-origin: 200% 50%;
        transform-origin: 200% 50%;
        -webkit-transform: rotate(160deg);
        transform: rotate(160deg)
    }
}

@keyframes bombout {
    0% {
        opacity: 1;
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    50% {
        opacity: 1;
        -webkit-transform-origin: 200% 50%;
        transform-origin: 200% 50%;
        -webkit-transform: rotate(160deg);
        transform: rotate(160deg)
    }

    100% {
        opacity: 0;
        -webkit-transform-origin: 200% 50%;
        transform-origin: 200% 50%;
        -webkit-transform: rotate(160deg);
        transform: rotate(160deg)
    }
}


/* -------------------- Text  bengin -------------------------- */

.hover-effect[data-effect-name="text-shadow"] p > span:hover {
    text-shadow: 0 1px 0 #ccc, 0 2px 0 #c9c9c9, 0 3px 0 #bbb, 0 4px 0 #b9b9b9, 0 5px 0 #aaa, 0 6px 1px rgba(0, 0, 0, 0.1), 0 0 5px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.3), 0 3px 5px rgba(0, 0, 0, 0.2), 0 5px 10px rgba(0, 0, 0, 0.25);
}

.hover-effect[data-effect-name="text-bottom-shadow"] p > span {
    position: relative;
}

.hover-effect[data-effect-name="text-bottom-shadow"] p > span:before {
        position: absolute;
        top: 100%;
        left: 5%;
        display: block;
        content: '';
        width: 90%;
        height: 10px;
        z-index: -1;
        opacity: 0;
        background: radial-gradient( ellipse at center, rgba(0, 0, 0, 0.35) 0%, rgba(0, 0, 0, 0) 80% );
        transition: all 0.3s linear;
    }

.hover-effect[data-effect-name="text-bottom-shadow"] p > span:hover {
        transform: translateY(-5px);
    }

 .hover-effect[data-effect-name="text-bottom-shadow"] p > span:hover:before {
            opacity: 1;
            transform: translateY(5px);
        }

.hover-effect[data-effect-name="text-3d-shadow"] p > span:hover {
    color: rgb(40, 80, 120);
    text-shadow: 0 0 1px currentColor, 2px 0 1px #083058, 0 2px 1px #98c0e8, 2px 1px 1px #083058, 1px 2px 1px #98c0e8, 2px 2px 1px #487098, 3px 1px 1px #083058, 1px 3px 1px #98c0e8, 3px 2px 1px #083058, 2px 3px 1px #98c0e8, 3px 3px 1px #487098, 4px 2px 1px #083058, 2px 4px 1px #98c0e8, 4px 3px 1px #083058, 3px 4px 1px #98c0e8, 4px 4px 1px #487098, 5px 3px 1px #083058, 3px 5px 1px #98c0e8, 5px 4px 1px #083058, 4px 5px 1px #98c0e8, 5px 5px 1px #487098;
}

 .hover-effect[data-effect-name="text-3d-shadow"] p > span:hover:before {
        color: rgb(40, 80, 120);
        text-shadow: 0 0 1px currentColor, -1px -1px 1px #003, 0 -1px 1px #003, 1px -1px 1px #003, 1px 0 1px #003, 1px 1px 1px #003, 0 1px 1px #003, -1px 1px 1px #003, -1px 0 1px #003;
    }

 .hover-effect[data-effect-name="text-3d-shadow"] p > span:hover:after {
        color: rgba(40, 80, 120, 0.1);
        text-shadow: 1px -1px 1px rgba(40, 80, 120, 0.8), -1px 1px 1px rgba(255, 255, 255, 0.8);
    }

.hover-effect[data-effect-name="text-bold-shadow"] p > span:hover {
    font-weight: bold;
    color: orange;
    text-shadow: 0 0 1px currentColor, -1px -1px 1px #030, 0 -1px 1px #030, 1px -1px 1px #030, 1px 0 1px #030, 1px 1px 1px #030, 0 1px 1px #030, -1px 1px 1px #030, -1px 0 1px #030;
}

/* -------------------- Text  end -------------------------- */

.hover-effect .smAreaC .yibuFrameContent:not(.image_Style6) img {
    transform: none !important;
}