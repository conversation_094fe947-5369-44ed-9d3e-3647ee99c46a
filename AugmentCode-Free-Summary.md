# 关于 `AugmentCode-Free` 项目

您好，您提供的链接 `https://github.com/BasicProtein/AugmentCode-Free` 指向一个 GitHub 项目。

根据项目介绍，这是一个通过特定方法来免费使用 AugmentCode 的方案，其中提到新账号可以获得600次免费的 Claude Sonnet 4 调用。<mcreference link="https://github.com/BasicProtein/AugmentCode-Free?tab=readme-ov-file#%E4%B8%AD%E6%96%87" index="0">0</mcreference>

该项目提供了一个基于 Python 的工具包，用于清理 VS Code 的本地数据库和修改遥测ID，以实现其目的。<mcreference link="https://github.com/BasicProtein/AugmentCode-Free?tab=readme-ov-file#%E4%B8%AD%E6%96%87" index="0">0</mcreference>

## 使用方法摘要

根据项目 `README` 文件，主要步骤如下：<mcreference link="https://github.com/BasicProtein/AugmentCode-Free?tab=readme-ov-file#%E4%B8%AD%E6%96%87" index="0">0</mcreference>

1.  **邀请团队成员**：使用一个任意状态的 AugmentCode 账号，在账户设置中创建一个团队(Team)，然后邀请一个新的小号（新邮箱）加入。<mcreference link="https://github.com/BasicProtein/AugmentCode-Free?tab=readme-ov-file#%E4%B8%AD%E6%96%87" index="0">0</mcreference>
2.  **激活小号**：用小号登录邮箱，点击收到的邀请链接完成激活。<mcreference link="https://github.com/BasicProtein/AugmentCode-Free?tab=readme-ov-file#%E4%B8%AD%E6%96%87" index="0">0</mcreference>
3.  **清理环境**：执行该 GitHub 项目提供的清理脚本，清理本地的配置。<mcreference link="https://github.com/BasicProtein/AugmentCode-Free?tab=readme-ov-file#%E4%B8%AD%E6%96%87" index="0">0</mcreference>
4.  **登录使用**：使用激活后的小号登录 AugmentCode 插件即可。<mcreference link="https://github.com/BasicProtein/AugmentCode-Free?tab=readme-ov-file#%E4%B8%AD%E6%96%87" index="0">0</mcreference>

该项目包含图形界面（GUI）和命令行（CLI）两种使用方式。

**请注意**：这些操作需要您在浏览器和本地环境中手动完成，我无法直接为您执行。这里是根据项目信息为您整理的摘要。