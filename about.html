<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关于我们 - 安徽春晟机械有限公司</title>
    <meta name="description" content="安徽春晟机械有限公司是专业从事减震器冲压件设计与生产的企业">
    <meta name="keywords" content="安徽春晟机械,减震器冲压件,企业介绍">
    
    <!-- 样式文件 -->
    <link href="pcstyle.css" rel="stylesheet" type="text/css">
    <link href="reset.css" rel="stylesheet" type="text/css">
    <link href="32_Pc_zh-CN.css" rel="stylesheet" type="text/css">
    <link href="css/custom.css" rel="stylesheet" type="text/css">
    <link href="css/customer-service-chat.css" rel="stylesheet" type="text/css">

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <!-- jQuery -->
    <script src="jquery-3.6.3.min.js"></script>
</head>
<body>
    <!-- 头部区域 -->
    <header class="header-section">
        <!-- Logo区域 -->
        <div class="logo-section" style="background-color: rgb(37, 37, 37); padding: 10px 0;">
            <div class="container" style="width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
                <div class="logo">
                    <a href="index.html">
                        <img src="16792504.png" alt="安徽春晟机械有限公司" style="height: 60px;">
                    </a>
                </div>
                <div class="user-actions">
                    <a href="login.html" class="login-btn" style="color: white; margin-right: 15px;">登录</a>
                    <a href="register.html" class="register-btn" style="background: #be131b; color: white; padding: 5px 15px;">注册</a>
                </div>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <nav class="navigation" style="background-color: #F5F5F5; border-bottom: 1px solid #ddd;">
            <div class="container" style="width: 1200px; margin: 0 auto;">
                <ul class="nav-menu" style="display: flex; list-style: none; margin: 0; padding: 0;">
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="index.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">网站首页</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="about.html" style="display: block; padding: 20px; color: white; background: #be131b; text-decoration: none; transition: all 0.3s;">关于我们</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">企业展示</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">工厂设备</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="products.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">产品中心</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="news.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">新闻中心</a>
                    </li>
                    <li style="flex: 1; text-align: center;">
                        <a href="contact.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">联系我们</a>
                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- 页面标题 -->
    <section class="page-header" style="background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('16719971.jpg') center/cover; padding: 80px 0; color: white; text-align: center;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <h1 style="font-size: 48px; margin-bottom: 20px;">关于我们</h1>
            <p style="font-size: 18px;">专业从事减震器冲压件设计与生产的企业</p>
        </div>
    </section>

    <!-- 公司简介 -->
    <section class="company-intro" style="padding: 80px 0; background: white;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <div class="intro-content" style="display: grid; grid-template-columns: 1fr 1fr; gap: 60px; align-items: center;">
                <div class="intro-text">
                    <h2 style="font-size: 32px; color: #222; margin-bottom: 20px;">安徽春晟机械有限公司</h2>
                    <p style="font-size: 16px; line-height: 1.8; color: #444; margin-bottom: 20px;">
                        本公司系专业从事减震器冲压件设计与生产的企业。公司坐落于安徽省广德经济开发区，地处苏浙皖三省交界处，交通便利，环境优雅。
                        距离上海230公里，杭州100公里，宁波280公里，南京230公里，拥有天然的地理优势。
                    </p>
                    <p style="font-size: 16px; line-height: 1.8; color: #444; margin-bottom: 20px;">
                        工厂占地40亩（建筑面积22000㎡），包括综合办公楼，生产车间，模具仓库，材料仓库，物流区域，员工宿舍，员工食堂，以及员工文娱场所等。
                    </p>
                    <p style="font-size: 16px; line-height: 1.8; color: #444; margin-bottom: 30px;">
                        公司拥有先进的生产设备和完善的质量管理体系，致力于为客户提供高品质的减震器冲压件产品和专业的技术服务。
                    </p>
                </div>
                <div class="intro-image">
                    <img src="16028205.jpg" alt="公司厂房" style="width: 100%; height: 400px; object-fit: cover; border-radius: 10px; box-shadow: 0 5px 20px rgba(0,0,0,0.1);">
                </div>
            </div>
        </div>
    </section>

    <!-- 企业文化 -->
    <section class="company-culture" style="padding: 80px 0; background: #f8f9fa;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <div class="section-header" style="text-align: center; margin-bottom: 50px;">
                <h2 style="font-size: 32px; color: #222; margin-bottom: 15px;">企业文化</h2>
                <p style="font-size: 16px; color: #666;">秉承专业、创新、诚信、共赢的企业理念</p>
            </div>
            
            <div class="culture-grid" style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 30px;">
                <div class="culture-item" style="background: white; padding: 30px; border-radius: 10px; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <div class="culture-icon" style="width: 60px; height: 60px; background: #be131b; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">🎯</div>
                    <h3 style="color: #333; margin-bottom: 15px;">专业</h3>
                    <p style="color: #666; line-height: 1.6;">专注减震器冲压件领域，不断提升专业技术水平</p>
                </div>
                
                <div class="culture-item" style="background: white; padding: 30px; border-radius: 10px; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <div class="culture-icon" style="width: 60px; height: 60px; background: #be131b; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">💡</div>
                    <h3 style="color: #333; margin-bottom: 15px;">创新</h3>
                    <p style="color: #666; line-height: 1.6;">持续技术创新，引进先进设备和工艺</p>
                </div>
                
                <div class="culture-item" style="background: white; padding: 30px; border-radius: 10px; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <div class="culture-icon" style="width: 60px; height: 60px; background: #be131b; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">🤝</div>
                    <h3 style="color: #333; margin-bottom: 15px;">诚信</h3>
                    <p style="color: #666; line-height: 1.6;">诚信经营，建立长期稳定的合作关系</p>
                </div>
                
                <div class="culture-item" style="background: white; padding: 30px; border-radius: 10px; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <div class="culture-icon" style="width: 60px; height: 60px; background: #be131b; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">🏆</div>
                    <h3 style="color: #333; margin-bottom: 15px;">共赢</h3>
                    <p style="color: #666; line-height: 1.6;">与客户、员工、合作伙伴实现共同发展</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 发展历程 -->
    <section class="company-history" style="padding: 100px 0; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); position: relative; overflow: hidden;">
        <!-- 背景装饰 -->
        <div style="position: absolute; top: -50px; right: -50px; width: 200px; height: 200px; background: linear-gradient(45deg, rgba(190, 19, 27, 0.1), rgba(190, 19, 27, 0.05)); border-radius: 50%; z-index: 1;"></div>
        <div style="position: absolute; bottom: -100px; left: -100px; width: 300px; height: 300px; background: linear-gradient(45deg, rgba(190, 19, 27, 0.05), rgba(190, 19, 27, 0.02)); border-radius: 50%; z-index: 1;"></div>

        <div class="container" style="width: 1200px; margin: 0 auto; position: relative; z-index: 2;">
            <div class="section-header" style="text-align: center; margin-bottom: 80px;">
                <div style="display: inline-block; background: linear-gradient(135deg, #be131b, #d32f2f); color: white; padding: 8px 20px; border-radius: 25px; font-size: 14px; font-weight: 600; margin-bottom: 20px; box-shadow: 0 4px 15px rgba(190, 19, 27, 0.3);">
                    🏆 企业发展
                </div>
                <h2 style="font-size: 42px; color: #222; margin-bottom: 20px; font-weight: 700; background: linear-gradient(135deg, #be131b, #d32f2f); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">发展历程</h2>
                <p style="font-size: 18px; color: #666; max-width: 600px; margin: 0 auto; line-height: 1.6;">见证我们从初创到行业领先的成长足迹，每一步都承载着创新与突破</p>
            </div>

            <div class="timeline" style="position: relative; max-width: 1000px; margin: 0 auto;">
                <!-- 主时间线 -->
                <div class="timeline-line" style="position: absolute; left: 50%; top: 0; bottom: 0; width: 4px; background: linear-gradient(to bottom, #be131b, #d32f2f, #be131b); transform: translateX(-50%); border-radius: 2px; box-shadow: 0 0 20px rgba(190, 19, 27, 0.3);"></div>

                <!-- 2000年 - 公司成立 -->
                <div class="timeline-item" style="position: relative; margin-bottom: 80px; display: flex; align-items: center; animation: fadeInUp 0.8s ease-out;">
                    <div class="timeline-content left" style="width: 45%; text-align: right; padding-right: 40px;">
                        <div class="timeline-card" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); border-left: 5px solid #be131b; transition: all 0.3s ease; position: relative;">
                            <div class="timeline-year" style="background: linear-gradient(135deg, #be131b, #d32f2f); color: white; padding: 8px 20px; border-radius: 25px; display: inline-block; margin-bottom: 15px; font-weight: 700; font-size: 16px; box-shadow: 0 4px 15px rgba(190, 19, 27, 0.3);">2000</div>
                            <h4 style="color: #333; margin-bottom: 15px; font-size: 20px; font-weight: 600;">🏭 公司成立</h4>
                            <p style="color: #666; line-height: 1.8; font-size: 15px;">公司前身为环春机械有限公司于2000年5月注册成立，开启了专业减震器冲压件制造的征程</p>
                            <div style="position: absolute; right: -10px; top: 50%; width: 0; height: 0; border-top: 10px solid transparent; border-bottom: 10px solid transparent; border-left: 10px solid white; transform: translateY(-50%);"></div>
                        </div>
                    </div>
                    <div class="timeline-dot" style="position: absolute; left: 50%; width: 20px; height: 20px; background: linear-gradient(135deg, #be131b, #d32f2f); border-radius: 50%; transform: translateX(-50%); border: 4px solid white; box-shadow: 0 0 20px rgba(190, 19, 27, 0.5); z-index: 3;"></div>
                    <div style="width: 45%;"></div>
                </div>

                <!-- 2004年 - 开始投产 -->
                <div class="timeline-item" style="position: relative; margin-bottom: 80px; display: flex; align-items: center; animation: fadeInUp 0.8s ease-out 0.2s; animation-fill-mode: both;">
                    <div style="width: 45%;"></div>
                    <div class="timeline-dot" style="position: absolute; left: 50%; width: 20px; height: 20px; background: linear-gradient(135deg, #be131b, #d32f2f); border-radius: 50%; transform: translateX(-50%); border: 4px solid white; box-shadow: 0 0 20px rgba(190, 19, 27, 0.5); z-index: 3;"></div>
                    <div class="timeline-content right" style="width: 45%; text-align: left; padding-left: 40px;">
                        <div class="timeline-card" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); border-right: 5px solid #be131b; transition: all 0.3s ease; position: relative;">
                            <div class="timeline-year" style="background: linear-gradient(135deg, #be131b, #d32f2f); color: white; padding: 8px 20px; border-radius: 25px; display: inline-block; margin-bottom: 15px; font-weight: 700; font-size: 16px; box-shadow: 0 4px 15px rgba(190, 19, 27, 0.3);">2004</div>
                            <h4 style="color: #333; margin-bottom: 15px; font-size: 20px; font-weight: 600;">🚀 开始投产</h4>
                            <p style="color: #666; line-height: 1.8; font-size: 15px;">厂房建设完成并正式开始投产，标志着公司从筹备阶段进入实际生产运营</p>
                            <div style="position: absolute; left: -10px; top: 50%; width: 0; height: 0; border-top: 10px solid transparent; border-bottom: 10px solid transparent; border-right: 10px solid white; transform: translateY(-50%);"></div>
                        </div>
                    </div>
                </div>

                <!-- 2012年 - 公司搬迁 -->
                <div class="timeline-item" style="position: relative; margin-bottom: 80px; display: flex; align-items: center; animation: fadeInUp 0.8s ease-out 0.4s; animation-fill-mode: both;">
                    <div class="timeline-content left" style="width: 45%; text-align: right; padding-right: 40px;">
                        <div class="timeline-card" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); border-left: 5px solid #be131b; transition: all 0.3s ease; position: relative;">
                            <div class="timeline-year" style="background: linear-gradient(135deg, #be131b, #d32f2f); color: white; padding: 8px 20px; border-radius: 25px; display: inline-block; margin-bottom: 15px; font-weight: 700; font-size: 16px; box-shadow: 0 4px 15px rgba(190, 19, 27, 0.3);">2012</div>
                            <h4 style="color: #333; margin-bottom: 15px; font-size: 20px; font-weight: 600;">🏢 战略搬迁</h4>
                            <p style="color: #666; line-height: 1.8; font-size: 15px;">公司更名为安徽春晟机械有限公司，整体搬迁至安徽广德经济开发区，开启新的发展篇章</p>
                            <div style="position: absolute; right: -10px; top: 50%; width: 0; height: 0; border-top: 10px solid transparent; border-bottom: 10px solid transparent; border-left: 10px solid white; transform: translateY(-50%);"></div>
                        </div>
                    </div>
                    <div class="timeline-dot" style="position: absolute; left: 50%; width: 20px; height: 20px; background: linear-gradient(135deg, #be131b, #d32f2f); border-radius: 50%; transform: translateX(-50%); border: 4px solid white; box-shadow: 0 0 20px rgba(190, 19, 27, 0.5); z-index: 3;"></div>
                    <div style="width: 45%;"></div>
                </div>

                <!-- 2017年 - 进军国际 -->
                <div class="timeline-item" style="position: relative; margin-bottom: 80px; display: flex; align-items: center; animation: fadeInUp 0.8s ease-out 0.6s; animation-fill-mode: both;">
                    <div style="width: 45%;"></div>
                    <div class="timeline-dot" style="position: absolute; left: 50%; width: 20px; height: 20px; background: linear-gradient(135deg, #be131b, #d32f2f); border-radius: 50%; transform: translateX(-50%); border: 4px solid white; box-shadow: 0 0 20px rgba(190, 19, 27, 0.5); z-index: 3;"></div>
                    <div class="timeline-content right" style="width: 45%; text-align: left; padding-left: 40px;">
                        <div class="timeline-card" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); border-right: 5px solid #be131b; transition: all 0.3s ease; position: relative;">
                            <div class="timeline-year" style="background: linear-gradient(135deg, #be131b, #d32f2f); color: white; padding: 8px 20px; border-radius: 25px; display: inline-block; margin-bottom: 15px; font-weight: 700; font-size: 16px; box-shadow: 0 4px 15px rgba(190, 19, 27, 0.3);">2017</div>
                            <h4 style="color: #333; margin-bottom: 15px; font-size: 20px; font-weight: 600;">🌍 进军国际</h4>
                            <p style="color: #666; line-height: 1.8; font-size: 15px;">通过IATF16949国际质量管理体系认证，正式开始拓展国际业务，产品远销海外</p>
                            <div style="position: absolute; left: -10px; top: 50%; width: 0; height: 0; border-top: 10px solid transparent; border-bottom: 10px solid transparent; border-right: 10px solid white; transform: translateY(-50%);"></div>
                        </div>
                    </div>
                </div>

                <!-- 2024年 - 数字化转型 -->
                <div class="timeline-item" style="position: relative; display: flex; align-items: center; animation: fadeInUp 0.8s ease-out 0.8s; animation-fill-mode: both;">
                    <div class="timeline-content left" style="width: 45%; text-align: right; padding-right: 40px;">
                        <div class="timeline-card" style="background: linear-gradient(135deg, #be131b, #d32f2f); color: white; padding: 30px; border-radius: 15px; box-shadow: 0 15px 40px rgba(190, 19, 27, 0.4); transition: all 0.3s ease; position: relative; border: 2px solid rgba(255,255,255,0.2);">
                            <div class="timeline-year" style="background: rgba(255,255,255,0.2); color: white; padding: 8px 20px; border-radius: 25px; display: inline-block; margin-bottom: 15px; font-weight: 700; font-size: 16px; backdrop-filter: blur(10px);">2024</div>
                            <h4 style="color: white; margin-bottom: 15px; font-size: 20px; font-weight: 600;">💻 数字化转型</h4>
                            <p style="color: rgba(255,255,255,0.9); line-height: 1.8; font-size: 15px;">启动全面数字化转型，建设智能化生产管理系统，迈向工业4.0时代</p>
                            <div style="position: absolute; right: -10px; top: 50%; width: 0; height: 0; border-top: 10px solid transparent; border-bottom: 10px solid transparent; border-left: 10px solid #be131b; transform: translateY(-50%);"></div>
                        </div>
                    </div>
                    <div class="timeline-dot" style="position: absolute; left: 50%; width: 24px; height: 24px; background: linear-gradient(135deg, #ffd700, #ffed4e); border-radius: 50%; transform: translateX(-50%); border: 4px solid white; box-shadow: 0 0 25px rgba(255, 215, 0, 0.6); z-index: 3; animation: pulse 2s infinite;"></div>
                    <div style="width: 45%;"></div>
                </div>
            </div>

            <!-- 成就统计 -->
            <div class="achievements" style="margin-top: 80px; background: white; padding: 40px; border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 30px; text-align: center;">
                    <div class="achievement-item">
                        <div style="font-size: 36px; font-weight: 700; color: #be131b; margin-bottom: 10px;">24+</div>
                        <div style="color: #666; font-size: 14px;">年专业经验</div>
                    </div>
                    <div class="achievement-item">
                        <div style="font-size: 36px; font-weight: 700; color: #be131b; margin-bottom: 10px;">200+</div>
                        <div style="color: #666; font-size: 14px;">员工团队</div>
                    </div>
                    <div class="achievement-item">
                        <div style="font-size: 36px; font-weight: 700; color: #be131b; margin-bottom: 10px;">40</div>
                        <div style="color: #666; font-size: 14px;">亩厂区面积</div>
                    </div>
                    <div class="achievement-item">
                        <div style="font-size: 36px; font-weight: 700; color: #be131b; margin-bottom: 10px;">100+</div>
                        <div style="color: #666; font-size: 14px;">合作客户</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- CSS动画 -->
        <style>
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            @keyframes pulse {
                0%, 100% {
                    transform: translateX(-50%) scale(1);
                }
                50% {
                    transform: translateX(-50%) scale(1.1);
                }
            }

            .timeline-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
            }

            .timeline-item:hover .timeline-dot {
                transform: translateX(-50%) scale(1.2);
            }

            @media (max-width: 768px) {
                .timeline-line {
                    left: 30px !important;
                }

                .timeline-item {
                    flex-direction: column !important;
                }

                .timeline-content {
                    width: 100% !important;
                    text-align: left !important;
                    padding: 0 0 0 60px !important;
                }

                .timeline-dot {
                    left: 30px !important;
                }

                .achievements {
                    grid-template-columns: repeat(2, 1fr) !important;
                }
            }
        </style>
    </section>

    <!-- 组织架构 -->
    <section class="organization" style="padding: 80px 0; background: #f8f9fa;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <div class="section-header" style="text-align: center; margin-bottom: 50px;">
                <h2 style="font-size: 32px; color: #222; margin-bottom: 15px;">组织架构</h2>
                <p style="font-size: 16px; color: #666;">主要管理人员15人 技术研发人员25人 一线员工160人</p>
            </div>

            <div class="org-chart" style="text-align: center; max-width: 900px; margin: 0 auto;">
                <!-- 董事长 -->
                <div class="org-level-1" style="margin-bottom: 30px;">
                    <div class="org-box" style="display: inline-block; background: #be131b; color: white; padding: 15px 30px; border-radius: 8px; font-weight: bold; font-size: 16px;">
                        董事长
                    </div>
                </div>

                <!-- 总经理 -->
                <div class="org-level-2" style="margin-bottom: 30px;">
                    <div class="org-box" style="display: inline-block; background: #be131b; color: white; padding: 15px 30px; border-radius: 8px; font-weight: bold; font-size: 16px;">
                        总经理
                    </div>
                </div>

                <!-- 高级副总和营销总代 -->
                <div class="org-level-3" style="display: flex; justify-content: center; gap: 80px; margin-bottom: 30px;">
                    <div class="org-box" style="background: #be131b; color: white; padding: 12px 25px; border-radius: 8px; font-weight: bold;">
                        高级副总
                    </div>
                    <div class="org-box" style="background: #be131b; color: white; padding: 12px 25px; border-radius: 8px; font-weight: bold;">
                        营销总代
                    </div>
                </div>

                <!-- 财务部 -->
                <div class="org-level-4" style="margin-bottom: 30px;">
                    <div class="org-box" style="display: inline-block; background: #be131b; color: white; padding: 12px 25px; border-radius: 8px; font-weight: bold;">
                        财务部
                    </div>
                </div>

                <!-- 各部门 -->
                <div class="org-level-5" style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap;">
                    <div class="org-box" style="background: #be131b; color: white; padding: 10px 20px; border-radius: 6px; font-weight: bold; font-size: 14px;">
                        业务部
                    </div>
                    <div class="org-box" style="background: #be131b; color: white; padding: 10px 20px; border-radius: 6px; font-weight: bold; font-size: 14px;">
                        生产部
                    </div>
                    <div class="org-box" style="background: #be131b; color: white; padding: 10px 20px; border-radius: 6px; font-weight: bold; font-size: 14px;">
                        品管部
                    </div>
                    <div class="org-box" style="background: #be131b; color: white; padding: 10px 20px; border-radius: 6px; font-weight: bold; font-size: 14px;">
                        技术部
                    </div>
                    <div class="org-box" style="background: #be131b; color: white; padding: 10px 20px; border-radius: 6px; font-weight: bold; font-size: 14px;">
                        采购部
                    </div>
                    <div class="org-box" style="background: #be131b; color: white; padding: 10px 20px; border-radius: 6px; font-weight: bold; font-size: 14px;">
                        行政部
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer style="background: #333; color: white; padding: 40px 0;">
        <div class="container" style="width: 1200px; margin: 0 auto; text-align: center;">
            <p>&copy; 2024 安徽春晟机械有限公司. 保留所有权利.</p>
            <p>地址：安徽省广德经济开发区 | 专业从事减震器冲压件设计与生产</p>
        </div>
    </footer>

    <!-- 浮动二维码 -->
    <div class="floating-qr-codes">
        <!-- 业务微信 -->
        <div class="qr-code-item" id="wechat-qr">
            <div class="qr-icon" title="业务微信">
                <img src="images/wechat-icon.svg" alt="微信" onerror="this.innerHTML='💬'">
            </div>
            <div class="qr-popup">
                <div class="qr-content">
                    <h4>业务微信</h4>
                    <div class="qr-image">
                        <img src="images/wechat-qr.svg" alt="业务微信二维码" onerror="this.innerHTML='<div style=\'width:120px;height:120px;background:#f0f0f0;display:flex;align-items:center;justify-content:center;border-radius:8px;\'>微信二维码</div>'">
                    </div>
                    <p>扫码添加业务微信</p>
                </div>
            </div>
        </div>

        <!-- 公众号 -->
        <div class="qr-code-item" id="official-qr">
            <div class="qr-icon" title="微信公众号">
                <img src="images/official-icon.svg" alt="公众号" onerror="this.innerHTML='📱'">
            </div>
            <div class="qr-popup">
                <div class="qr-content">
                    <h4>微信公众号</h4>
                    <div class="qr-image">
                        <img src="images/official-qr.svg" alt="微信公众号二维码" onerror="this.innerHTML='<div style=\'width:120px;height:120px;background:#f0f0f0;display:flex;align-items:center;justify-content:center;border-radius:8px;\'>公众号二维码</div>'">
                    </div>
                    <p>扫码关注公众号</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 浮动二维码样式 -->
    <style>
        .floating-qr-codes {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .qr-code-item {
            position: relative;
        }

        .qr-icon {
            width: 50px;
            height: 50px;
            background: #be131b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(190, 19, 27, 0.3);
            transition: all 0.3s ease;
            color: white;
            font-size: 20px;
        }

        .qr-icon:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(190, 19, 27, 0.4);
        }

        .qr-icon img {
            width: 24px;
            height: 24px;
            filter: brightness(0) invert(1);
        }

        .qr-popup {
            position: absolute;
            right: 60px;
            top: 50%;
            transform: translateY(-50%);
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
            padding: 20px;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            min-width: 180px;
            border: 1px solid #e9ecef;
        }

        .qr-popup::after {
            content: '';
            position: absolute;
            right: -8px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 8px solid white;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
        }

        .qr-code-item:hover .qr-popup {
            opacity: 1;
            visibility: visible;
        }

        .qr-content h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
            text-align: center;
            font-weight: 600;
        }

        .qr-image {
            text-align: center;
            margin-bottom: 15px;
        }

        .qr-image img {
            width: 120px;
            height: 120px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .qr-content p {
            margin: 0;
            color: #666;
            font-size: 14px;
            text-align: center;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .floating-qr-codes {
                right: 15px;
                gap: 12px;
            }

            .qr-icon {
                width: 45px;
                height: 45px;
                font-size: 18px;
            }

            .qr-icon img {
                width: 20px;
                height: 20px;
            }

            .qr-popup {
                right: 55px;
                padding: 15px;
                min-width: 160px;
            }

            .qr-image img {
                width: 100px;
                height: 100px;
            }

            .qr-content h4 {
                font-size: 14px;
            }

            .qr-content p {
                font-size: 12px;
            }
        }

        /* 超小屏幕 */
        @media (max-width: 480px) {
            .floating-qr-codes {
                right: 10px;
                bottom: 80px;
                top: auto;
                transform: none;
                flex-direction: row;
                gap: 10px;
            }

            .qr-icon {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .qr-popup {
                right: auto;
                left: 50%;
                bottom: 50px;
                top: auto;
                transform: translateX(-50%);
                min-width: 140px;
            }

            .qr-popup::after {
                right: auto;
                left: 50%;
                bottom: -8px;
                top: auto;
                transform: translateX(-50%);
                border-left: 8px solid transparent;
                border-right: 8px solid transparent;
                border-top: 8px solid white;
                border-bottom: none;
            }

            .qr-image img {
                width: 80px;
                height: 80px;
            }
        }
    </style>

    <!-- JavaScript -->
    <script src="js/supabase-config.js"></script>
    <script src="js/main.js"></script>

    <!-- 客服系统脚本 -->
    <script src="js/customer-service-chat.js"></script>

    <!-- 浮动二维码交互脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加点击事件，移动端可以点击显示二维码
            const qrItems = document.querySelectorAll('.qr-code-item');

            qrItems.forEach(item => {
                const icon = item.querySelector('.qr-icon');
                const popup = item.querySelector('.qr-popup');
                let isActive = false;

                // 移动端点击切换
                icon.addEventListener('click', function(e) {
                    e.stopPropagation();

                    // 关闭其他弹窗
                    qrItems.forEach(otherItem => {
                        if (otherItem !== item) {
                            otherItem.classList.remove('active');
                        }
                    });

                    // 切换当前弹窗
                    isActive = !isActive;
                    if (isActive) {
                        item.classList.add('active');
                    } else {
                        item.classList.remove('active');
                    }
                });
            });

            // 点击其他地方关闭弹窗
            document.addEventListener('click', function() {
                qrItems.forEach(item => {
                    item.classList.remove('active');
                });
            });

            // 阻止弹窗内部点击事件冒泡
            document.querySelectorAll('.qr-popup').forEach(popup => {
                popup.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            });
        });
    </script>

    <!-- 移动端激活状态样式 -->
    <style>
        .qr-code-item.active .qr-popup {
            opacity: 1 !important;
            visibility: visible !important;
        }

        .qr-code-item.active .qr-icon {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(190, 19, 27, 0.4);
        }
    </style>
</body>
</html>
